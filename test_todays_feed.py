#!/usr/bin/env python3
"""
Test script for the updated /todays-feed endpoint functionality.
This script tests the comprehensive categorization system that includes all posts.
"""

import asyncio
import json
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.utils.feed_categorizer import categorize_feed_posts, FeedPostItem

async def test_todays_feed():
    """Test the updated todays-feed functionality with sample data."""
    
    # Load test data from todays feed.json
    try:
        with open('todays feed.json', 'r', encoding='utf-8') as f:
            test_data = json.load(f)
    except FileNotFoundError:
        print("Error: 'todays feed.json' file not found!")
        return
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return
    
    # Extract posts and persona keywords from test data
    posts_data = test_data.get('posts', [])
    general_persona_keywords = test_data.get('general_persona_keywords', [])
    content_persona_keywords = test_data.get('content_persona_keywords', [])
    network_persona_keywords = test_data.get('network_persona_keywords', [])
    
    print(f"Loaded {len(posts_data)} posts from test data")
    print(f"General persona keywords: {general_persona_keywords}")
    print(f"Content persona keywords: {content_persona_keywords}")
    print(f"Network persona keywords: {network_persona_keywords}")
    print("-" * 80)
    
    # Convert to FeedPostItem objects
    posts = []
    for post_data in posts_data:
        post = FeedPostItem(
            activity_urn=post_data.get('activity_urn', ''),
            text=post_data.get('text', ''),
            total_reactions=post_data.get('total_reactions', 0),
            total_comments=post_data.get('total_comments', 0),
            total_shares=post_data.get('total_shares', 0),
            author_urn=post_data.get('author_urn', '')
        )
        posts.append(post)
    
    print(f"Testing comprehensive categorization with {len(posts)} posts...")
    
    try:
        # Test the updated categorize_feed_posts function
        categorized_feed = await categorize_feed_posts(
            posts=posts,
            general_persona_keywords=general_persona_keywords,
            content_persona_keywords=content_persona_keywords,
            network_persona_keywords=network_persona_keywords
        )
        
        print(f"\n✅ Successfully categorized feed!")
        print(f"Number of categories: {len(categorized_feed)}")
        
        # Display results
        total_posts_in_categories = 0
        for i, category in enumerate(categorized_feed, 1):
            category_name = category.get('category', 'Unknown')
            posts_in_category = len(category.get('posts', []))
            total_posts_in_categories += posts_in_category
            
            print(f"\n{i}. Category: {category_name}")
            print(f"   Posts: {posts_in_category}")
            print(f"   Summary: {category.get('summary', 'No summary')[:100]}...")
            
            # Show first few posts in each category
            category_posts = category.get('posts', [])
            for j, post in enumerate(category_posts[:2]):  # Show first 2 posts
                post_text = post.get('text', '')[:80] + '...' if len(post.get('text', '')) > 80 else post.get('text', '')
                print(f"     Post {j+1}: {post_text}")
        
        print(f"\n📊 Summary:")
        print(f"   Total posts in input: {len(posts)}")
        print(f"   Total posts in categories: {total_posts_in_categories}")
        print(f"   All posts included: {'✅ YES' if total_posts_in_categories == len(posts) else '❌ NO'}")
        
        # Verify that all posts are included (no filtering)
        if total_posts_in_categories == len(posts):
            print("\n🎉 SUCCESS: All posts are included in the categorized feed!")
            print("   The filtering has been successfully removed.")
        else:
            print(f"\n⚠️  WARNING: {len(posts) - total_posts_in_categories} posts are missing!")
            print("   Some posts may still be filtered out.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during categorization: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Updated /todays-feed Endpoint Functionality")
    print("=" * 80)
    
    # Run the test
    success = asyncio.run(test_todays_feed())
    
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
        sys.exit(1)
