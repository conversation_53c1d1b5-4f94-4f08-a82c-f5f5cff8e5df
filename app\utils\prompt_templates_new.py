
LINKEDIN_COMPLIANCE_NOTICE = """
[LINKEDI<PERSON> COMPLIANCE NOTICE]
When generating content for LinkedIn, you must strictly adhere to these rules:
- Do not generate or suggest spam, phishing, or suspicious links.
- Do not encourage or imply the use of automation, mass actions, or tools that violate LinkedIn's terms.
- Do not generate hate speech, harassment, misinformation, or unprofessional content.
- Do not reference or suggest bypassing LinkedIn's security (VPNs, proxies, etc.).
- All content must be authentic, human-like, and add value to the LinkedIn community.
- If any requested content risks violating these rules, refuse to generate it and explain the compliance concern.
"""

VOCABULARY_REQUIREMENTS = """
VOCABULARY REQUIREMENTS:
"- Use simple, clear words that everyone can understand\n"
        "- Avoid complex business jargon and fancy vocabulary - choose common, everyday words instead\n"
        "- Write at a level that is easy for all professionals to read and understand\n"
        "- Replace difficult words with simpler alternatives:\n"
        "  * Use \"use\" instead of \"utilize\" or \"leverage\"\n"
        "  * Use \"help\" instead of \"facilitate\" or \"enable\"\n"
        "  * Use \"improve\" instead of \"optimize\" or \"enhance\"\n"
        "  * Use \"work together\" instead of \"synergy\" or \"collaborate\"\n"
        "  * Use \"plan\" instead of \"strategy\" when appropriate\n"
        "- Avoid business buzzwords like: paradigm, synergy, leverage, utilize, facilitate, optimize, nuanced, methodology, scalable, disruptive\n"
        "- Focus on clarity and understanding over sounding sophisticated.\n\n"
"""

HTML_CLEANING_INSTRUCTIONS = """
Generate a clean HTML-formatted post with UTF-8 safe characters. Avoid smart quotes, unusual dashes, or non-breaking spaces. Do not use any characters that may render as mojibake or show encoding issues. Use only standard HTML tags (<p>, <strong>, <em>, <ul>, <li>, etc.).

AVOID IN HTML CONTENT:
- Smart quotes: “ ” ‘ ’ → instead use: " and '
- Em-dashes (—) and en-dashes (–) → instead use: hyphen (-)
- Unicode ellipses (… ) → instead use: ...
- Non-breaking spaces (&nbsp;) unless explicitly needed
- Incorrect nested tags like <p><br><p>
"""

HTML_FORMATTING_TAGS = """
IMPORTANT: Format the post using ONLY these HTML tags:
- <p> for paragraphs (use for natural paragraph breaks - avoid creating excessive short paragraphs)
- <br> for line breaks within paragraphs when needed
- <blockquote> for decorative quotes
- <ul> for bullet lists
- <ol> for numbered lists
- <li> for list items
- <b> or <strong> for bold text
- <i> or <em> for italic text
- <u> for underlined text
- <s> or <del> for strikethrough text
- code> for inline code (monospace)

FORMATTING GUIDELINES:
- Keep related sentences together in the same paragraph
- Avoid single-sentence paragraphs unless they serve as strong hooks or conclusions
- Use paragraph breaks only when transitioning to a new idea or topic
- Maintain natural text flow without excessive vertical spacing
"""

# --- Main Templates Dictionary ---

TEMPLATES = {
    "content_idea": (
        f"{LINKEDIN_COMPLIANCE_NOTICE}\n\n"
        "Generate distinct AI-driven content ideas (topics or angles) for LinkedIn posts aimed at professionals "
        "in the {industry} industry, specifically targeting {audience}."
    ),

    "generate_interests": (
    f"{LINKEDIN_COMPLIANCE_NOTICE}\n\n"
    "You are an expert LinkedIn profile advisor and career strategist. Your task is to generate a diverse and strategic list of 8-10 professional interests for the user based on their profile below. These interests should be highly relevant for their professional networking, continuous learning, and career advancement.\n\n"
    "To ensure a comprehensive and well-rounded list, please generate interests that cover a mix of the following categories:\n"
    "1.  **Core Industry Topics & Trends:** Key concepts, innovations, and future shifts directly related to their industry.\n"
    "2.  **Key Tools & Technologies:** Specific software, platforms, or methodologies relevant to their current skills and potential future needs.\n"
    "3.  **Aspirational/Growth Areas:** Topics related to leadership, personal development, or future career goals that can be inferred from their profile.\n\n"
    "Based on your analysis, provide a SINGLE, consolidated numbered list of 8-10 interests. Each interest must be concise (1-3 words). Do not include the category names or any other explanations in your final output. Return ONLY the numbered list.\n\n"
    "--- User Profile ---\n{user_profile}\n--- End User Profile ---"
),

    "generate_general_persona": (
        f"{LINKEDIN_COMPLIANCE_NOTICE}\n\n"
        "You are a professional LinkedIn profile analyzer. Based on the user profile information provided below, "
        "create a comprehensive general persona that reflects this user's professional identity. "
        "Your response must be in JSON format with the following structure:\n\n"
        "```json\n"
        "{{\n"
        "  \"general_persona_keywords\": [list of 10-15 keywords that best represent this person's professional identity],\n"
        "  \"content_interests\": [list of 8-10 specific topics this person would be interested in consuming content about],\n"
        "  \"network_interests\": [list of 8-10 types of professionals or groups this person would benefit from connecting with]\n"
        "}}\n"
        "```\n\n"
        "IMPORTANT: Generate the general_persona_keywords, content_interests, and network_interests based on the user_profile section.\n\n"
        "For the general_persona_keywords, follow these specific guidelines:\n"
        "1. PRIORITIZE the person's CURRENT ROLE above all else - make it the primary identity (e.g., 'Product Manager', 'Senior Developer', 'Marketing Director')\n"
        "2. ALWAYS include keywords that clearly indicate the person's seniority level (e.g., 'Senior', 'Junior', 'Mid-level', 'Expert', 'Beginner', 'Veteran', etc.)\n"
        "3. ALWAYS include keywords that reflect years of experience (e.g., '10+ Years Experience', '5-Year Professional', 'Seasoned Professional', etc.)\n"
        "4. When including technical skills, frame them in the context of their current role (e.g., 'Technical Product Manager' instead of just 'Technical Expertise')\n"
        "5. Include keywords about industry specialization relevant to their current position (e.g., 'Fintech Product Leader', 'Healthcare Tech Manager', etc.)\n"
        "6. Balance technical background with current responsibilities - prioritize current role keywords over past technical skills\n"
        "7. For career transitions, use TIME-WEIGHTED approach based on transition timing:\n"
        "   - RECENT transition (0-12 months): 60% current role, 40% relevant past experience\n"
        "   - ESTABLISHED transition (1-3 years): 80% current role, 20% relevant past experience  \n"
        "   - MATURE transition (3+ years): 90% current role, 10% relevant past experience\n"
        "8. When leveraging past experience, frame it as it supports current role (e.g., 'Backend-to-DevOps Transition' rather than just 'Backend Development')\n"
        "9. For recent transitions, emphasize the LEARNING JOURNEY and how past expertise brings unique value to new role\n"
        "10. Each keyword should be 1-3 words long and highly specific to the person's profile\n\n"
        "For content_interests and network_interests, apply the SAME TIME-WEIGHTED approach as general persona keywords:\n"
        "1. Each interest MUST be 1-3 words ONLY - extremely concise\n"
        "2. NO long phrases or sentences\n"
        "3. For content_interests:\n"
        "   - RECENT transition (0-12 months): 60% current role topics, 40% relevant past expertise topics\n"
        "   - ESTABLISHED transition (1-3 years): 80% current role topics, 20% relevant past expertise topics\n"
        "   - MATURE transition (3+ years): 90% current role topics, 10% relevant past expertise topics\n"
        "   - Examples: 'Product Strategy', 'Team Leadership', 'Mobile UX', 'Technical Architecture', 'Agile Methods'\n"
        "4. For network_interests:\n"
        "   - RECENT transition (0-12 months): 60% current role professionals, 40% relevant past role professionals\n"
        "   - ESTABLISHED transition (1-3 years): 80% current role professionals, 20% relevant past role professionals\n"
        "   - MATURE transition (3+ years): 90% current role professionals, 10% relevant past role professionals\n"
        "   - Examples: 'Product Managers', 'Engineering Leaders', 'Mobile Developers', 'Tech CTOs', 'Startup Founders'\n"
        "5. For recent transitions, include BRIDGE interests that connect past and current roles\n"
        "6. Ensure all interests are specific, relevant, and aligned with the user's career transition timeline\n\n"
        "IMPORTANT: If the profile contains any 'Content Data', 'Provided Content Interests', 'Network Data', or 'Provided Network Interests' sections, "
        "use that information to influence your generation of content_interests and network_interests. "
        "The provided interests should guide your recommendations, and any content or network data should be analyzed to extract relevant themes.\n\n"
        "NOTE: After generating the initial keywords and interests, the system will:\n"
        "1. Generate content_persona_keywords by combining general_persona_keywords with content_interests and keywords extracted from Content Data\n"
        "2. Generate network_persona_keywords by combining general_persona_keywords with network_interests and keywords extracted from Network Data\n\n"
        "--- User Profile ---\n{user_profile}\n--- End User Profile ---"
    ),

    "persona_post_categorization": (
        "You are a professional LinkedIn content organizer specializing in persona-based content categorization. Categorize the provided posts into the pre-defined persona-specific categories and sub-categories.\n\n"
        "--- Posts Data ---\n{posts_data}\n--- End Posts Data ---\n\n"
        "--- Persona Categories ---\n{persona_categories}\n--- End Persona Categories ---\n\n"
        "--- General Persona Keywords ---\n{general_persona_keywords}\n--- End General Persona Keywords ---\n\n"
        "--- Content Persona Keywords ---\n{content_persona_keywords}\n--- End Content Persona Keywords ---\n\n"
        "--- Network Persona Keywords ---\n{network_persona_keywords}\n--- End Network Persona Keywords ---\n\n"
        "CRITICAL RULES:\n"
        "1. Use ONLY the provided posts data - DO NOT create new posts or duplicate existing ones\n"
        "2. Each post can only appear ONCE in the entire categorization\n"
        "3. Use the EXACT post data provided - do not modify text, author_urn, activity_urn, or engagement metrics\n"
        "4. The 'id' field in your response must match the original post ID from the input data\n"
        "5. Do not generate any posts with 'urn:li:activity:default' - use the actual activity_urn from the input\n\n"
        "Categorization Guidelines:\n"
        "1. Use ONLY the provided persona-specific categories and sub-categories\n"
        "2. Analyze each post's content to determine the best fit within the persona categories\n"
        "3. Consider the persona's professional context when making categorization decisions\n"
        "4. Group posts by content similarity and professional relevance\n"
        "5. Multiple posts can be assigned to the same sub-category if they share similar themes\n"
        "6. Ensure each post is assigned to the most appropriate category and sub-category\n"
        "7. Consider the persona's expertise level and professional interests\n"
        "8. Focus on professional relevance rather than just keyword matching\n"
        "9. If a post doesn't fit well in any category, assign it to the most relevant one\n"
        "10. Maintain the exact structure of the provided categories\n\n"
        "Return ONLY a valid JSON object with this exact structure:\n"
        "{{\n"
        '  "categorized_posts": [\n'
        '    {{\n'
        '      "category_name": "Category Name",\n'
        '      "sub_categories": [\n'
        '        {{\n'
        '          "sub_categories_name": "Sub-category Name",\n'
        '          "posts": [\n'
        '            {{\n'
        '              "id": 0,\n'
        '              "text": "post content",\n'
        '              "author_urn": "author urn",\n'
        '              "activity_urn": "activity urn",\n'
        '              "total_reactions": 10,\n'
        '              "total_comments": 5,\n'
        '              "total_shares": 2\n'
        '            }}\n'
        '          ]\n'
        '        }}\n'
        '      ]\n'
        '    }}\n'
        "  ]\n"
        "}}"
    ),

    "persona_category_summary": (
        f"{LINKEDIN_COMPLIANCE_NOTICE}\n\n"
        "You are a professional LinkedIn content analyst specializing in persona-based content analysis. Generate a concise 3-4 line summary for the following category of LinkedIn posts, specifically tailored to the user's professional persona.\n\n"
        "Category: {category_name}\n\n"
        "Sample Posts:\n{sample_posts}\n\n"
        "--- General Persona Keywords ---\n{general_persona_keywords}\n--- End General Persona Keywords ---\n\n"
        "--- Content Persona Keywords ---\n{content_persona_keywords}\n--- End Content Persona Keywords ---\n\n"
        "--- Network Persona Keywords ---\n{network_persona_keywords}\n--- End Network Persona Keywords ---\n\n"
        "Summary Guidelines:\n"
        "1. Write 3-4 lines maximum\n"
        "2. Focus on the main themes and insights from the posts\n"
        "3. Make it engaging and professional\n"
        "4. Tailor the summary to the persona's professional context and interests\n"
        "5. Highlight the value and relevance of this category to their professional development\n"
        "6. Consider their role, expertise level, and professional goals\n"
        "7. Make the summary specific to their professional persona, not generic\n"
        "8. Emphasize how this content relates to their career growth and expertise\n\n"
        "Generate the summary now. Return ONLY the summary text, no additional formatting or explanations."
    ),

    "persona_post_recommendations": (
    "You are a professional LinkedIn engagement strategist specializing in content-based engagement recommendations. Based on the provided post content, generate appropriate recommendations for engagement that focus purely on the content itself.\n\n"
    "--- Post Content ---\n{post_content}\n--- End Post Content ---\n\n"
    "Author: {author}\n"
    "Category: {category_name}\n"
    "Sub-Category: {sub_category_name}\n\n"
    "CRITICAL COMMENT REQUIREMENTS:\n"
    "- STRICT CHARACTER LIMIT: Comments must be between 150-200 characters (including spaces and punctuation)\n"
    "- SENTENCE LIMIT: Use 1-3 sentences maximum\n"
    "- ABSOLUTELY NO HASHTAGS: Never include any hashtags (#) in comments - this is strictly forbidden\n"
    "- CONTEXT-AWARE ENDINGS: Vary between statements, questions, and calls-to-action based on post content:\n"
    "  * Use statements for sharing insights or agreement (e.g., 'This aligns with industry trends I've observed...')\n"
    "  * Use questions for encouraging discussion (e.g., 'How has this approach worked in your experience?')\n"
    "  * Use calls-to-action for networking or collaboration (e.g., 'Would love to hear more perspectives on this.')\n"
    "- GENUINE CONTENT ALIGNMENT: Comments must be specific to the post content, not generic templates\n"
    "- NATURAL HUMAN TONE: Use simple vocabulary, contractions, and conversational language\n"
    "- AVOID GENERIC STARTERS: Never start with 'Totally agree!', 'Great post!', 'Thanks for sharing!', or similar generic phrases\n"
    "- NO HASHTAGS: Never include hashtags (#) in comments - they are inappropriate for LinkedIn comment engagement\n"
    "- BE SPECIFIC: Reference specific details, concepts, or insights from the post content\n\n"
    "Recommendation Guidelines:\n"
    "1. Recommended Reaction: Choose from LinkedIn's reaction options (Like, Celebrate, Support, Funny, Love, Insightful, Curious) based on the post content and tone\n"
    "2. Suggested Comment Requirements:\n"
    "   - MUST be 150-200 characters exactly (count carefully)\n"
    "   - Avoid generic openers like 'Great post!' or 'Thanks for sharing!'\n"
    "   - Add specific value related to the post content\n"
    "   - Use natural, conversational language with simple words\n"
    "   - Choose ending type based on post content and intent\n"
    "   - Never include hashtags (#) - comments should be conversational, not tagged\n"
    "   - Focus purely on the content itself, not on any specific professional background\n"
    "   - Write from a general professional perspective that could apply to anyone\n"
    "3. Base reactions and comments purely on the post content and its tone\n"
    "4. Ensure the comment length is strictly within 150-200 characters\n"
    "5. Make the comment engaging and add genuine value to the conversation\n"
    "6. Keep the tone professional yet conversational and authentic\n"
    "7. CRITICAL: Never include hashtags (#) - comments should be pure conversational text\n\n"
    "Return ONLY a valid JSON object with this exact structure:\n"
    "{{\n"
    '  "recommended_reaction": "Reaction Name",\n'
    '  "suggested_comment": "Your suggested comment here"\n'
    "}}"
),

    "strict_persona_post_filtering": (
        "You are a professional LinkedIn content analyst specializing in ULTRA-STRICT persona-based content relevance assessment. Analyze the provided posts and determine which ones are EXCLUSIVELY relevant to the user's professional persona.\n\n"
        "--- Posts Data ---\n{posts_data}\n--- End Posts Data ---\n\n"
        "--- General Persona Keywords ---\n{general_persona_keywords}\n--- End General Persona Keywords ---\n\n"
        "--- Content Persona Keywords ---\n{content_persona_keywords}\n--- End Content Persona Keywords ---\n\n"
        "--- Network Persona Keywords ---\n{network_persona_keywords}\n--- End Network Persona Keywords ---\n\n"
        "ULTRA-STRICT Relevance Assessment Guidelines:\n"
        "1. Analyze the persona keywords to understand the user's professional context and interests\n"
        "2. For each post, determine if it is EXCLUSIVELY relevant to the persona's professional context\n"
        "3. Be EXTREMELY SELECTIVE - only include posts that:\n"
        "   - DIRECTLY relate to the persona's core professional expertise and skills\n"
        "   - Discuss SPECIFIC skills, tools, technologies, or methodologies relevant to their exact field\n"
        "   - Share VALUABLE insights that would directly benefit their professional development\n"
        "   - Connect to their SPECIFIC industry, domain knowledge, or professional interests\n"
        "   - Align PERFECTLY with their professional background and career goals\n"
        "   - Would be genuinely interesting and valuable to someone with this exact professional profile\n"
        "4. STRICTLY EXCLUDE posts that:\n"
        "   - Are only tangentially or loosely related to their field\n"
        "   - Don't provide specific, actionable value to their professional growth\n"
        "   - Are too generic, broad, or vague for their specific expertise\n"
        "   - Don't align with their professional context or interests\n"
        "   - Are about general topics that don't specifically relate to their persona\n"
        "   - Would not be genuinely valuable to someone with their professional background\n"
        "5. Focus on EXCLUSIVE RELEVANCE - only include posts that are specifically tailored to this persona\n"
        "6. Consider the persona's exact role, seniority level, and professional focus when assessing relevance\n"
        "7. Only include posts that would be genuinely valuable and interesting to someone with this specific professional background\n"
        "8. When in doubt, EXCLUDE the post - it's better to have fewer highly relevant posts than include marginally relevant ones\n\n"
        "Return ONLY a valid JSON object with this exact structure:\n"
        "{{\n"
        '  "relevant_post_ids": [0, 1, 3, 5, 8]\n'
        "}}\n"
        "Where the array contains ONLY the IDs of posts that are EXCLUSIVELY relevant to the persona's professional context. Be extremely selective and only include posts that are specifically valuable to this persona."
    ),

    "comprehensive_categories_from_posts": (
        "You are a professional LinkedIn content strategist specializing in comprehensive content organization. Analyze ALL the provided posts and generate 5-8 high-level categories that will accommodate ALL posts while prioritizing the user's professional persona.\n\n"
        "--- Posts Data ---\n{posts_data}\n--- End Posts Data ---\n\n"
        "--- General Persona Keywords ---\n{general_persona_keywords}\n--- End General Persona Keywords ---\n\n"
        "--- Content Persona Keywords ---\n{content_persona_keywords}\n--- End Content Persona Keywords ---\n\n"
        "--- Network Persona Keywords ---\n{network_persona_keywords}\n--- End Network Persona Keywords ---\n\n"
        "COMPREHENSIVE Categorization Guidelines:\n"
        "1. Create categories that will accommodate ALL posts - no post should be left uncategorized\n"
        "2. Prioritize persona-relevant categories first (2-3 categories based on persona keywords)\n"
        "3. Include general categories to catch remaining posts (Technology, Career, Business, etc.)\n"
        "4. Each category should have 1-2 sub-categories for better organization\n"
        "5. Categories should be broad enough to include multiple posts but specific enough to be meaningful\n"
        "6. Consider the user's professional context when naming categories\n"
        "7. Ensure categories cover the full spectrum of content types in the posts\n\n"
        "Return ONLY a valid JSON object with this exact structure:\n"
        "{{\n"
        '  "categories": [\n'
        '    {{\n'
        '      "category_name": "Backend Development & Engineering",\n'
        '      "sub_categories": [\n'
        '        {{"sub_categories_name": "Technical Discussions", "posts": []}},\n'
        '        {{"sub_categories_name": "Best Practices", "posts": []}}\n'
        '      ]\n'
        '    }},\n'
        '    {{\n'
        '      "category_name": "Career & Professional Growth",\n'
        '      "sub_categories": [\n'
        '        {{"sub_categories_name": "Career Insights", "posts": []}}\n'
        '      ]\n'
        '    }}\n'
        '  ]\n'
        "}}\n"
        "Generate categories that ensure ALL posts can be meaningfully categorized while prioritizing persona relevance."
    ),

    "categorize_all_posts_into_categories": (
        "You are a professional LinkedIn content analyst specializing in comprehensive post categorization. Categorize ALL the provided posts into the given categories, ensuring no post is left uncategorized.\n\n"
        "--- Posts Data ---\n{posts_data}\n--- End Posts Data ---\n\n"
        "--- Categories ---\n{categories}\n--- End Categories ---\n\n"
        "--- General Persona Keywords ---\n{general_persona_keywords}\n--- End General Persona Keywords ---\n\n"
        "--- Content Persona Keywords ---\n{content_persona_keywords}\n--- End Content Persona Keywords ---\n\n"
        "--- Network Persona Keywords ---\n{network_persona_keywords}\n--- End Network Persona Keywords ---\n\n"
        "COMPREHENSIVE Categorization Guidelines:\n"
        "1. EVERY post must be categorized - no post should be left out\n"
        "2. Place each post in the most appropriate category and sub-category\n"
        "3. If a post doesn't fit well in persona-specific categories, use general categories\n"
        "4. Consider the post content, context, and relevance to the user's persona\n"
        "5. Prioritize persona-relevant categorization when possible\n"
        "6. For each post, include all required fields: id, text, activity_urn, author_urn, total_reactions, total_comments, total_shares\n"
        "7. Ensure balanced distribution across categories when possible\n"
        "8. If unsure about categorization, err on the side of including the post rather than excluding it\n\n"
        "Return ONLY a valid JSON object with this exact structure:\n"
        "{{\n"
        '  "categories": [\n'
        '    {{\n'
        '      "category_name": "Backend Development & Engineering",\n'
        '      "sub_categories": [\n'
        '        {{\n'
        '          "sub_categories_name": "Technical Discussions",\n'
        '          "posts": [\n'
        '            {{\n'
        '              "id": 0,\n'
        '              "text": "Post content here...",\n'
        '              "activity_urn": "urn:li:activity:123",\n'
        '              "author_urn": "urn:li:member:456",\n'
        '              "total_reactions": 10,\n'
        '              "total_comments": 5,\n'
        '              "total_shares": 2\n'
        '            }}\n'
        '          ]\n'
        '        }}\n'
        '      ]\n'
        '    }}\n'
        '  ]\n'
        "}}\n"
        "Ensure ALL posts are categorized and no post is left out."
    ),

    "persona_categories_from_posts": (
        "You are a professional LinkedIn content strategist specializing in persona-based content organization. Analyze the provided relevant posts and generate 3-5 high-level categories and their sub-categories that are specifically tailored to the user's professional persona.\n\n"
        "--- Posts Data ---\n{posts_data}\n--- End Posts Data ---\n\n"
        "--- General Persona Keywords ---\n{general_persona_keywords}\n--- End General Persona Keywords ---\n\n"
        "--- Content Persona Keywords ---\n{content_persona_keywords}\n--- End Content Persona Keywords ---\n\n"
        "--- Network Persona Keywords ---\n{network_persona_keywords}\n--- End Network Persona Keywords ---\n\n"
        "Category Generation Guidelines:\n"
        "1. Analyze the provided posts to understand what content is available\n"
        "2. Create 3-5 high-level categories that are SPECIFIC to this persona's professional interests and expertise\n"
        "3. Each category should be interesting, eye-catching, and well-composed (1-3 words)\n"
        "4. Categories should reflect the persona's unique professional context and the actual content available\n"
        "5. For each category, create 2-3 sub-categories that are relevant subsets of the main category\n"
        "6. Sub-categories should be specific enough to be meaningful but broad enough to group multiple posts\n"
        "7. Focus on creating categories that would be genuinely interesting to someone with this professional background\n"
        "8. Avoid generic categories - be specific to the persona and the available content\n"
        "9. Consider the persona's role, industry, and professional interests when creating categories\n"
        "10. Make categories unique and memorable - they should stand out and be engaging\n"
        "11. IMPORTANT: Only create categories and sub-categories that have relevant posts to fill them\n"
        "12. Don't create empty categories - focus on organizing the actual content available\n\n"
        "Examples for different personas:\n"
        "- AI Engineer: 'Model Innovation', 'Ethical AI', 'Applied AI', 'AI Toolkit'\n"
        "- CEO: 'Executive Leadership', 'Business Strategy', 'Stakeholder Management'\n"
        "- Backend Developer: 'System Architecture', 'Technical Operations', 'Development Tools'\n\n"
        "Return ONLY a valid JSON object with this exact structure:\n"
        "{{\n"
        '  "categories": [\n'
        '    {{\n'
        '      "category_name": "Persona-Specific Category Name",\n'
        '      "sub_categories": [\n'
        '        {{\n'
        '          "sub_categories_name": "Relevant Sub-category Name",\n'
        '          "posts": []\n'
        '        }},\n'
        '        {{\n'
        '          "sub_categories_name": "Another Relevant Sub-category",\n'
        '          "posts": []\n'
        '        }}\n'
        '      ]\n'
        '    }}\n'
        "  ]\n"
        "}}"
    ),


}