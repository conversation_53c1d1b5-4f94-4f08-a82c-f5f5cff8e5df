#!/usr/bin/env python3
"""
Test the /todays-feed endpoint directly with HTTP requests.
"""

import requests
import json

def test_todays_feed_endpoint():
    """Test the /todays-feed endpoint with sample data."""
    
    # Load test data
    try:
        with open('todays feed.json', 'r', encoding='utf-8') as f:
            test_data = json.load(f)
    except FileNotFoundError:
        print("Error: 'todays feed.json' file not found!")
        return
    
    # Prepare the request payload
    payload = {
        "posts": test_data.get("posts", []),
        "general_persona_keywords": test_data.get("general_persona_keywords", []),
        "content_persona_keywords": test_data.get("content_persona_keywords", []),
        "network_persona_keywords": test_data.get("network_persona_keywords", [])
    }
    
    print(f"Testing /todays-feed endpoint with {len(payload['posts'])} posts...")
    print(f"General persona keywords: {payload['general_persona_keywords']}")
    
    try:
        # Make the request to the endpoint
        response = requests.post(
            "http://localhost:8000/todays-feed",
            json=payload,
            timeout=300  # 5 minutes timeout
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n✅ SUCCESS! Endpoint returned {response.status_code}")
            print(f"Response type: {type(result)}")
            print(f"Response structure: {json.dumps(result, indent=2)[:500]}...")

            # Handle both list and dict response formats
            categories = result if isinstance(result, list) else result.get('categories', [])

            if categories:
                print(f"Number of categories: {len(categories)}")

                total_posts = 0
                for i, category in enumerate(categories, 1):
                    if isinstance(category, dict):
                        category_name = category.get('category_name', category.get('category', 'Unknown'))
                        posts_count = 0

                        # Count posts in subcategories
                        for subcategory in category.get('sub_categories', []):
                            posts_count += len(subcategory.get('posts', []))

                        # Also check direct posts
                        posts_count += len(category.get('posts', []))

                        total_posts += posts_count
                        print(f"{i}. {category_name}: {posts_count} posts")
                    else:
                        print(f"{i}. Invalid category type: {type(category)}")

                print(f"\nTotal posts in response: {total_posts}")
                print(f"Original posts count: {len(payload['posts'])}")
                print(f"All posts included: {'✅ YES' if total_posts == len(payload['posts']) else '❌ NO'}")

                if total_posts == len(payload['posts']):
                    print("\n🎉 SUCCESS: All posts are included in the categorized feed!")
                    print("   The filtering has been successfully removed.")
                else:
                    print(f"\n⚠️  WARNING: {len(payload['posts']) - total_posts} posts are missing!")
                    print("   Some posts may still be filtered out.")
            else:
                print("No categories found in response")
            
        else:
            print(f"❌ ERROR! Status code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🧪 Testing /todays-feed Endpoint")
    print("=" * 50)
    test_todays_feed_endpoint()
