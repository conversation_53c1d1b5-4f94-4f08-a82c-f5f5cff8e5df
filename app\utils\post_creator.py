import re
import concurrent.futures
import hashlib
import asyncio
import logging
import random
import time
import os
import httpx # Use httpx for asynchronous network requests
from functools import lru_cache
from typing import List, Dict, Any, Optional
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
from app.utils.hashtag_generator import generate_hashtags
from app.utils.html_cleaner import clean_html_content


# --- NEW: Centralized helper to parse the varied responses from the generation function ---
def _parse_post_generation_response(response: Any) -> Optional[Dict[str, Any]]:
    """
    Parses various response formats from the post generation model into a single, standard post dictionary.
    Handles dicts, lists, and strings.
    """
    post_content = ""
    framework = "Unknown"
    
    if isinstance(response, dict) and "posts" in response and response["posts"]:
        posts_list = response["posts"]
        if isinstance(posts_list, list) and len(posts_list) > 0:
            first_post = posts_list[0]
            if isinstance(first_post, dict):
                post_content = first_post.get("content", "")
                framework = first_post.get("framework", "Unknown")
            else:
                post_content = str(first_post)
    elif isinstance(response, list) and len(response) > 0:
        first_item = response[0]
        if isinstance(first_item, dict):
            post_content = first_item.get("content", "")
            framework = first_item.get("framework", "Unknown")
        else:
            post_content = str(first_item)
    elif isinstance(response, str):
        post_content = response
    
    if not post_content.strip():
        return None
        
    return {"content": post_content, "framework": framework}


def generate_hashtags_for_post(post_content, interests=None):
    """Generate hashtags for a LinkedIn post."""
    return generate_hashtags(post_content, interests)

def add_intelligent_emojis_to_post(post_content, persona_keywords=None, content_interests=None):
    """
    Add contextually relevant emojis to a post using intelligent content analysis.

    Args:
        post_content (str): The original post content
        persona_keywords (list): User's persona keywords for context
        content_interests (list): User's content interests for context

    Returns:
        str: Post content with intelligently selected emojis added
    """
    from app.utils.model_initializer import model

    # Create context information for better emoji selection
    context_info = ""
    if persona_keywords:
        context_info += f"User's professional background: {', '.join(persona_keywords[:5])}\n"
    if content_interests:
        context_info += f"Content interests: {', '.join(content_interests[:5])}\n"

    # Create an intelligent emoji selection prompt
    emoji_prompt = f"""
You are an expert in LinkedIn content optimization and emoji selection. Analyze the following LinkedIn post and add 2-4 contextually relevant, professional emojis that enhance the message.

{context_info}

CONTENT ANALYSIS REQUIREMENTS:
1. Analyze the post's main themes, topics, and emotional tone.
2. Identify key concepts that would benefit from emoji emphasis.
3. Consider the professional context and industry relevance.
4. Determine optimal placement points for maximum impact.

EMOJI SELECTION CRITERIA:
- Choose emojis that directly relate to the specific content themes.
- Select emojis appropriate for a professional but human audience on LinkedIn.
- Avoid generic or overused emojis unless they perfectly match the content.
- Consider industry-specific emojis when relevant (tech: 💻🔧⚡, business: 📊💼🎯, etc.).
- Match the emotional tone of the content (inspirational, informative, celebratory, empathetic, a touch of humor, etc.).

PLACEMENT GUIDELINES:
- Place emojis at natural pause points or emphasis moments.
- Use emojis to break up longer text sections.
- Position emojis where they enhance meaning, not distract.
- Integrate emojis smoothly into the content flow.
- Vary placement (beginning, middle, end) based on content structure.

QUANTITY GUIDELINES:
- Use 2-4 emojis total based on content length and complexity.
- Shorter posts (under 500 chars): 2-3 emojis
- Longer posts (500+ chars): 3-4 emojis
- Adjust quantity based on natural emphasis points.

Original post:
{post_content}

Return the enhanced post with intelligently selected emojis integrated naturally. Maintain the exact HTML structure and formatting. Do not add explanations or comments - return only the enhanced post content.
"""

    try:
        # Generate the enhanced post with intelligent emoji selection
        response = model.generate_content(emoji_prompt)
        enhanced_content = response.text.strip()

        # Clean up any markdown formatting with proper error handling
        if "```html" in enhanced_content:
            parts = enhanced_content.split("```html", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    enhanced_content = sub_parts[0].strip()
        elif "```" in enhanced_content:
            parts = enhanced_content.split("```", 1)
            if len(parts) > 1:
                enhanced_content = parts[1].strip()

        # Remove any remaining code block markers
        enhanced_content = enhanced_content.replace("```html", "").replace("```", "").strip()

        return enhanced_content

    except Exception as e:
        print(f"Error adding intelligent emojis to post: {e}")
        return post_content  # Return original content if emoji addition fails

@lru_cache(maxsize=500)
def generate_search_query_from_content(content: str) -> str:
    """Uses the AI model to generate a concise search query from post content."""
    try:
        prompt = f"""
        Based on the following LinkedIn post, what is the best 5-10 words search query to find a highly relevant article?
        Post: "{content}"
        Respond with ONLY the search query.
        """
        response = model.generate_content(prompt, use_cache=True)
        search_query = response.text.strip().replace('"', '')
        return search_query
    except Exception as e:
        logging.error(f"Error generating search query: {e}")
        return " ".join(content.split()[:10])

# --- UPDATED: Asynchronous URL fetching using Tavily API ---
async def fetch_related_url(query: str) -> Optional[str]:
    """Asynchronously fetch a recent news URL using the Tavily API."""
    tavily_key = os.environ.get("TAVILY_API_KEY")
    if not tavily_key:
        logging.warning("TAVILY_API_KEY not set. Returning placeholder.")
        return f"https://example.com/article/placeholder-for-{query.replace(' ', '-')[:20]}"

    try:
        # Import Tavily client
        from tavily import TavilyClient
        client = TavilyClient(api_key=tavily_key)

        logging.info(f"Fetching URL with Tavily for query: '{query}'")

        # Use asyncio to run the blocking Tavily search in a thread
        import asyncio
        from functools import partial

        loop = asyncio.get_running_loop()

        # Create a partial function for the blocking search
        blocking_search = partial(
            client.search,
            query=query,
            search_depth="basic",
            days=30,      # Still useful to prioritize recent events
            max_results=3,
        )

        # Run the blocking search in a thread pool
        response = await loop.run_in_executor(None, blocking_search)

        # Extract the first relevant URL
        results = response.get('results', [])
        if results and isinstance(results, list):
            for result in results:
                if isinstance(result, dict) and 'url' in result:
                    url = result['url']
                    logging.info(f"Found relevant URL: {url}")
                    return url

        logging.warning(f"No relevant URLs found for query: '{query}'")
        return None
    except httpx.RequestError as e:
        logging.error(f"HTTP error calling SERP API: {e}")
        return None
    except Exception as e:
        logging.error(f"Error processing SERP API response: {e}")
        return None

def analyze_user_intent_and_perspective(user_prompt: str) -> Dict[str, Any]:
    """
    ***UPGRADED***: Now distinguishes between personal achievements and spotlighting a third party.
    """
    from app.utils.model_initializer import model

    if not user_prompt or not user_prompt.strip():
        return {
            "intent_type": "general",
            "persona_focus": "none",
            "is_persona_based": False,
            "is_truly_personal": False,
            "content_complexity": "direct_announcement",
            "sentiment": "neutral",
            "confidence": 0.5,
            "reasoning": "Fallback analysis due to empty prompt"
        }

    analysis_prompt = f"""
    Analyze the user's prompt for a LinkedIn post. Determine its intent, content style, and the appropriate way to use the user's persona.

    User Prompt: "{user_prompt}"

    Provide a detailed analysis in the following JSON format:

    {{
        "intent_type": "personal_story|personal_achievement|third_party_spotlight|hiring_announcement|advice|educational|promotional|commentary|event_announcement",
        "persona_focus": "deep|contextual|none",
        "content_complexity": "direct_announcement|strategic_narrative",
        "sentiment": "positive|neutral|negative",
        "confidence": 0.0-1.0,
        "reasoning": "Brief explanation for your choices."
    }}

    Analysis Guidelines:

    1.  **INTENT_TYPE (CRITICAL DISTINCTION):**
        -   `personal_story`: A post ABOUT the user's own deep journey (e.g., "I am looking for a job").
        -   `personal_achievement`: A post where the user shares THEIR OWN accomplishment (e.g., "I hosted an event," "I published an article"). Must be first-person.
        -   `third_party_spotlight`: A post ABOUT ANOTHER PERSON or company. The user is the narrator, but the subject is external (e.g., "Waqas Ahmad, CTO of Imagination Holdings," "Congratulations to our partner company on their new launch").

    2.  **PERSONA_FOCUS:**
        -   `deep`: For `personal_story` intents.
        -   `contextual`: For first-person `personal_achievement` or `hiring_announcement`. The user is the narrator of their own news.
        -   `none`: For ALL other intents, including `third_party_spotlight`, `advice`, and `educational` posts.

    CRITICAL RULES:
    -   "I am looking for a job" -> `intent_type: "personal_story"`, `persona_focus: "deep"`.
    -   "I hosted an event" -> `intent_type: "personal_achievement"`, `persona_focus: "contextual"`.
    -   "Waqas Ahmad, a CTO of Imagination Holdings" -> `intent_type: "third_party_spotlight"`, `persona_focus: "none"`.
    -   "Tips for resume writing" -> `intent_type: "advice"`, `persona_focus: "none"`.

    Return ONLY the JSON object.
    """

    try:
        response = model.generate_content(analysis_prompt, use_cache=False)
        analysis_text = response.text.strip()

        # Clean up the response to extract JSON with proper error handling
        if "```json" in analysis_text:
            parts = analysis_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    analysis_text = sub_parts[0].strip()
        elif "```" in analysis_text:
            parts = analysis_text.split("```", 1)
            if len(parts) > 1:
                analysis_text = parts[1].strip()

        # Parse the JSON response
        import json
        analysis_result = json.loads(analysis_text)
        
        # This flag is now simpler and more robustly determined by persona_focus.
        is_persona_based = analysis_result.get("persona_focus", "none") != "none"
        analysis_result['is_persona_based'] = is_persona_based
        
        # Backwards compatibility for the old is_truly_personal logic if needed elsewhere
        analysis_result['is_truly_personal'] = (analysis_result.get("persona_focus") == 'deep')

        # Set a default complexity if missing
        if 'content_complexity' not in analysis_result:
            analysis_result['content_complexity'] = 'direct_announcement'
        if 'sentiment' not in analysis_result:
            analysis_result['sentiment'] = 'neutral'

        return analysis_result

    except Exception as e:
        print(f"Error in intent analysis: {str(e)}")
        # Return safe defaults
        return {
            "intent_type": "general",
            "persona_focus": "none",
            "is_persona_based": False,
            "is_truly_personal": False,
            "content_complexity": "direct_announcement",
            "sentiment": "neutral",
            "confidence": 0.5,
            "reasoning": "Fallback analysis due to processing error"
        }

def intelligent_tone_assignment(selected_frameworks: List[Dict], user_prompt: str, content_analysis: Dict) -> List[Dict]:
    """
    AI-powered dynamic tone assignment that analyzes content and frameworks
    to generate optimal tones for each of the 3 post variants.

    Args:
        selected_frameworks: List of 3 selected frameworks
        user_prompt: Original user prompt
        content_analysis: Analysis of content intent and themes

    Returns:
        List of frameworks with assigned tones and guidance
    """
    from app.utils.model_initializer import model

    # Create comprehensive tone assignment prompt
    frameworks_info = "\n".join([
        f"Framework {i+1}: {fw['name']} - {fw['description']} | Best for: {', '.join(fw.get('best_for', []))}"
        for i, fw in enumerate(selected_frameworks)
    ])

    content_type = content_analysis.get("intent_type", "informative")
    key_themes = content_analysis.get("key_themes", [])
    target_audience = content_analysis.get("target_audience", "professionals")

    tone_assignment_prompt = f"""
You are an expert content strategist specializing in tone optimization for LinkedIn posts. Analyze the user's content and frameworks to assign the most effective tone for each of the 3 post variants.

USER PROMPT: "{user_prompt}"
CONTENT TYPE: {content_type}
KEY THEMES: {', '.join(key_themes)}
TARGET AUDIENCE: {target_audience}

SELECTED FRAMEWORKS:
{frameworks_info}

TONE ASSIGNMENT REQUIREMENTS:
1. Generate 3 DISTINCT tones that complement each framework and the overall content
2. Each tone should be optimized for the specific framework's strengths
3. Ensure variety across the 3 tones while maintaining content relevance
4. Consider the target audience and content type when selecting tones
5. Match tones to framework characteristics (e.g., PAS → problem-solving, AIDA → engaging)

AVAILABLE TONE CATEGORIES (choose and customize):
- Authoritative: confident, expert, credible, definitive
- Conversational: friendly, approachable, relatable, casual
- Inspirational: motivating, uplifting, encouraging, visionary
- Analytical: data-driven, logical, systematic, research-based
- Thought-provoking: questioning, challenging, reflective, insightful
- Problem-solving: solution-focused, practical, helpful, actionable
- Narrative: storytelling, personal, experiential, journey-focused
- Educational: teaching, informative, explanatory, knowledge-sharing
- Engaging: interactive, discussion-starting, community-building
- Professional: polished, industry-focused, business-oriented
- Empathetic: understanding, supportive, compassionate, human
- Innovative: forward-thinking, creative, cutting-edge, pioneering

TONE MATCHING GUIDELINES:
- AIDA Framework → Engaging, Persuasive, or Action-oriented tones
- PAS Framework → Problem-solving, Authoritative, or Analytical tones
- Before-After-Bridge → Educational, Problem-solving, or Progressive tones
- Listicle → Educational, Practical, or Organized tones
- Question-led → Thought-provoking, Engaging, or Interactive tones
- Data-Driven Persuasion → Analytical, Authoritative, or Evidence-based tones
- Credible Spotlight → Professional, Appreciative, or Industry-focused tones
- Counterintuitive Leadership → Thought-provoking, Challenging, or Insightful tones

Return your analysis in this JSON format:
{{
    "tone_assignments": [
        {{
            "framework_name": "Framework Name",
            "primary_tone": "Primary Tone Name",
            "tone_description": "Brief description of the tone approach",
            "style_attributes": ["attribute1", "attribute2", "attribute3"],
            "approach_guidance": "How to apply this tone with this framework",
            "voice_characteristics": "Specific voice characteristics for this combination",
            "framework_tone_synergy": "Why this tone works perfectly with this framework"
        }}
    ],
    "overall_strategy": "How the 3 tones work together to create variety and engagement",
    "audience_alignment": "How these tones align with the target audience"
}}

IMPORTANT: Ensure each tone is distinct and optimized for its specific framework while maintaining overall content coherence.
"""

    try:
        response = model.generate_content(tone_assignment_prompt, use_cache=False)
        tone_text = response.text.strip()

        # Clean up the response to extract JSON with proper error handling
        if "```json" in tone_text:
            parts = tone_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    tone_text = sub_parts[0].strip()
        elif "```" in tone_text:
            parts = tone_text.split("```", 1)
            if len(parts) > 1:
                tone_text = parts[1].strip()

        # Parse the JSON response
        import json
        tone_result = json.loads(tone_text)

        # Merge tone assignments back into frameworks with defensive programming
        tone_assignments = tone_result.get("tone_assignments", [])
        if not isinstance(tone_assignments, list):
            tone_assignments = []

        for i, framework in enumerate(selected_frameworks):
            if i < len(tone_assignments) and isinstance(tone_assignments[i], dict):
                tone_assignment = tone_assignments[i]
                framework.update({
                    "assigned_tone": tone_assignment.get("primary_tone", "Professional"),
                    "tone_description": tone_assignment.get("tone_description", ""),
                    "style_attributes": tone_assignment.get("style_attributes", []),
                    "approach_guidance": tone_assignment.get("approach_guidance", ""),
                    "voice_characteristics": tone_assignment.get("voice_characteristics", ""),
                    "framework_tone_synergy": tone_assignment.get("framework_tone_synergy", "")
                })
            else:
                # Fallback tone assignment
                fallback_tones = ["Professional", "Conversational", "Analytical"]
                framework.update({
                    "assigned_tone": fallback_tones[i % len(fallback_tones)] if fallback_tones else "Professional",
                    "tone_description": "Fallback tone assignment",
                    "style_attributes": ["clear", "engaging", "professional"],
                    "approach_guidance": "Apply tone naturally to framework structure",
                    "voice_characteristics": "Professional and accessible",
                    "framework_tone_synergy": "Complementary tone for framework"
                })

        # Add overall strategy to the framework analysis
        for framework in selected_frameworks:
            framework["overall_tone_strategy"] = tone_result.get("overall_strategy", "")
            framework["audience_alignment"] = tone_result.get("audience_alignment", "")

        return selected_frameworks

    except Exception as e:
        print(f"Error in intelligent tone assignment: {str(e)}")
        # Fallback to diverse tone assignment
        fallback_tones = [
            {
                "assigned_tone": "Authoritative",
                "tone_description": "Expert and confident approach",
                "style_attributes": ["confident", "knowledgeable", "clear"],
                "approach_guidance": "Share expertise with authority",
                "voice_characteristics": "Professional expert voice",
                "framework_tone_synergy": "Authority builds credibility"
            },
            {
                "assigned_tone": "Conversational",
                "tone_description": "Friendly and approachable style",
                "style_attributes": ["friendly", "relatable", "accessible"],
                "approach_guidance": "Connect personally with audience",
                "voice_characteristics": "Warm and engaging voice",
                "framework_tone_synergy": "Conversation builds engagement"
            },
            {
                "assigned_tone": "Thought-provoking",
                "tone_description": "Challenging and insightful perspective",
                "style_attributes": ["questioning", "insightful", "reflective"],
                "approach_guidance": "Challenge thinking and inspire reflection",
                "voice_characteristics": "Thoughtful and provocative voice",
                "framework_tone_synergy": "Questions drive deeper engagement"
            }
        ]

        for i, framework in enumerate(selected_frameworks):
            framework.update(fallback_tones[i % len(fallback_tones)])

        return selected_frameworks
    
def analyze_prompt_for_recency(user_prompt: str) -> Dict[str, Any]:
    """
    Uses the AI to determine if a prompt requires a real-time web search for recent events.
    """
    from app.utils.model_initializer import model

    analysis_prompt = f"""
    You are an expert news analyst. Your task is to determine if the following user prompt requires a real-time web search to be answered accurately.
    Focus on topics that are time-sensitive, like current events, recent announcements, new product launches, or situations that change rapidly.

    User Prompt: "{user_prompt}"

    Guidelines:
    - If the prompt is about a recent event (e.g., "recent floods," "new AI model launch," "today's tech news"), it requires a search.
    - If the prompt is an evergreen topic (e.g., "10 principles of marketing," "how to be a good leader," "benefits of teamwork"), it does not require a search.
    - If the prompt is a personal story (e.g., "my friend is looking for a job"), it does not require a search.

    Provide your analysis in the following JSON format. If a search is needed, create an optimized, neutral search query.

    {{
        "requires_search": boolean,
        "search_query": "Optimized search query or null"
    }}

    Return ONLY the JSON object.
    """
    try:
        response = model.generate_content(analysis_prompt, use_cache=False)
        analysis_text = response.text.strip()

        if "```json" in analysis_text:
            parts = analysis_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    analysis_text = sub_parts[0].strip() 
        
        import json
        return json.loads(analysis_text)
    except Exception as e:
        print(f"Error analyzing prompt for recency: {e}")
        return {"requires_search": False, "search_query": None}


def perform_tavily_search(query: str) -> Optional[str]:
    """
    Performs a sequential search using the Tavily API, prioritizing the most recent results.
    It searches in the following order: past day, past week, past month, past year, 
    and finally performs a default search if no time-filtered results are found.
    """
    tavily_api_key = os.environ.get("TAVILY_API_KEY")
    if not tavily_api_key:
        logging.warning("TAVILY_API_KEY not set. Skipping search.")
        return None
        
    try:
        from tavily import TavilyClient
        client = TavilyClient(api_key=tavily_api_key)
    except ImportError:
        logging.error("TavilyClient could not be imported. Please install with 'pip install tavily-python'.")
        return None

    # Define the timeframes to search in order of priority (day, week, month, year)
    # These 'tbs' values are passed to the underlying search engine.
    timeframes = [
        {'tbs': 'qdr:d', 'label': 'past day'},
        {'tbs': 'qdr:w', 'label': 'past week'},
        {'tbs': 'qdr:m', 'label': 'past month'},
        {'tbs': 'qdr:y', 'label': 'past year'}
    ]

    try:
        # Sequentially search through the defined timeframes
        for timeframe in timeframes:
            logging.info(f"Performing Tavily search for '{query}' within the {timeframe['label']}...")
            
            # The Tavily client allows passing extra keyword arguments to the search engine API.
            # Here, we use 'tbs' for Google Search's time filter.
            response = client.search(
                query=query, 
                search_depth="basic", 
                max_results=5,
                include_raw_content=False, # We only need the summarized content
                # Pass the time filter parameter
                tbs=timeframe['tbs'] 
            )
            
            results = response.get('results', [])
            if results:
                logging.info(f"Found {len(results)} results for the {timeframe['label']}.")
                # Format the results into a concise context block
                context = [f"Source: {res.get('url', 'N/A')}\nContent: {res.get('content', 'No content available.')}" for res in results]
                return "\n---\n".join(context)
            else:
                logging.info(f"No results found for the {timeframe['label']}. Broadening search...")

        # If no results are found in any specified timeframe, perform a default search
        logging.info("No recent results found. Performing a default search without time constraints...")
        response = client.search(query=query, search_depth="basic", max_results=5)
        
        results = response.get('results', [])
        if results:
            logging.info(f"Found {len(results)} results in the default search.")
            context = [f"Source: {res.get('url', 'N/A')}\nContent: {res.get('content', 'No content available.')}" for res in results]
            return "\n---\n".join(context)
            
        logging.warning(f"Tavily search for '{query}' returned no results even with the default search.")
        return None
        
    except Exception as e:
        logging.error(f"An error occurred during the Tavily search for query '{query}': {e}")
        return None


def generate_multiple_intents(user_prompt: str) -> List[Dict[str, str]]:
    """
    Analyzes a user prompt and generates up to three distinct intents or angles for a LinkedIn post.
    """
    from app.utils.model_initializer import model

    intent_generation_prompt = f"""
    You are an expert content strategist. Analyze the following user prompt for a LinkedIn post and identify up to 3 distinct potential intents or angles the user might have. Frame each intent as a clear, actionable goal for a post.

    User Prompt: "{user_prompt}"

    For each intent, provide a concise name and a brief description.

    Example for "UX design principles":
    {{
        "intents": [
            {{
                "name": "Educational Listicle",
                "description": "Create a post that lists and briefly explains core UX design principles for beginners."
            }},
            {{
                "name": "Strategic Importance",
                "description": "Write a post explaining how UX design principles directly impact business goals like user retention and revenue."
            }},
            {{
                "name": "Thought Leadership",
                "description": "Develop a post that discusses a counterintuitive or often-overlooked UX principle, challenging the audience's perspective."
            }}
        ]
    }}

    Return ONLY the JSON object.
    """
    try:
        response = model.generate_content(intent_generation_prompt, use_cache=False)
        intent_text = response.text.strip()

        # Clean up the response to extract JSON
        if "```json" in intent_text:
            parts = intent_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    intent_text = sub_parts[0].strip() 
        
        import json
        intent_result = json.loads(intent_text)
        return intent_result.get("intents", [])

    except Exception as e:
        print(f"Error in generating multiple intents: {str(e)}")
        # Fallback to a single, general intent
        return [{"name": "General Informative", "description": f"Create a general post about {user_prompt}"}]


def classify_prompt_and_select_frameworks(user_prompt: str, general_persona_keywords: List[str]) -> Dict[str, Any]:
    """
    ***MODIFIED***: Generates multiple intents and selects the single best framework for each intent.
    """
    # Step 1: Use the enhanced AI-powered intent analysis for persona context
    intent_analysis = analyze_user_intent_and_perspective(user_prompt)
    is_persona_based = intent_analysis.get("is_persona_based", False)
    
    # Step 2: Generate up to 3 distinct intents for content variety
    generated_intents = generate_multiple_intents(user_prompt)

    # Step 3: Define available frameworks
    available_frameworks = [
        # NEW FRAMEWORK for simple, on-point posts
        {
            "name": "General & Direct Post",
            "full_name": "General & Direct Content Framework",
            "best_for": ["Event", "Awareness", "Engagement"],
            "description": "A straightforward and clear post that gets directly to the point. Ideal for announcements, celebrations, and simple topics."
        },
        {
            "name": "AIDA",
            "full_name": "Attention-Interest-Desire-Action",
            "best_for": ["Awareness", "Engagement", "Branding"],
            "description": "Get attention, build interest, create want, and ask for action"
        },
        {
            "name": "PAS",
            "full_name": "Problem-Agitation-Solution",
            "best_for": ["Authority", "Informative"],
            "description": "Point out a problem, show why it hurts, and give the solution"
        },
        {
            "name": "Before-After-Bridge",
            "full_name": "Before-After-Bridge Framework",
            "best_for": ["Informative", "Authority"],
            "description": "Show current situation, ideal situation, and how to get there"
        },
        {
            "name": "Listicle",
            "full_name": "List-based Content Framework",
            "best_for": ["Informative", "Engagement"],
            "description": "Clear list format with useful tips"
        },
        {
            "name": "Question-led",
            "full_name": "Question-led Engagement Framework",
            "best_for": ["Engagement", "Authority"],
            "description": "Start with interesting question and give insights"
        },
        {
            "name": "Data-Driven Persuasion",
            "full_name": "Data-Driven Persuasion Framework",
            "best_for": ["Authority", "Informative"],
            "description": "Start with surprising statistics, explain why they matter, and give one clear action step"
        },
        {
            "name": "Credible Spotlight",
            "full_name": "Credible Spotlight Framework",
            "best_for": ["Authority", "Branding"],
            "description": "Highlight real people or organizations while connecting to personal values and bigger missions"
        },
        {
            "name": "Counterintuitive Leadership Truth",
            "full_name": "Counterintuitive Leadership Truth Framework",
            "best_for": ["Authority", "Engagement"],
            "description": "Challenge common beliefs with surprising insights and give better ways to do things"
        }
    ]
    selected_intent_framework_pairs = []
    used_framework_names = set()

    for intent in generated_intents:
        best_framework = None
        highest_score = -1

        # Find the best framework for the current intent's name (e.g., "Educational Listicle")
        shuffled_frameworks = random.sample(available_frameworks, len(available_frameworks))
        
        for framework in shuffled_frameworks:
            # Skip frameworks already used to ensure variety
            if framework["name"] in used_framework_names:
                continue

            score = 0
            # Check if the intent name is a good match for the framework's "best_for" categories
            if intent['name'] in framework['best_for']:
                score = 2
            # Use string matching for partial relevance
            elif any(word.lower() in framework['name'].lower() for word in intent['name'].split()):
                score = 1
            
            if score > highest_score:
                highest_score = score
                best_framework = framework
        
        # If no specific framework matches, pick a random unused one as a fallback
        if not best_framework:
            available_fallbacks = [f for f in available_frameworks if f['name'] not in used_framework_names]
            if available_fallbacks:
                best_framework = random.choice(available_fallbacks)

        if best_framework:
            selected_intent_framework_pairs.append({
                "intent": intent,
                "framework": best_framework
            })
            used_framework_names.add(best_framework['name'])

    # Ensure we have 3 pairs for generation
    while len(selected_intent_framework_pairs) < 3:
        available_fallbacks = [f for f in available_frameworks if f['name'] not in used_framework_names]
        if not available_fallbacks: break # Avoid infinite loop
        
        fallback_framework = random.choice(available_fallbacks)
        selected_intent_framework_pairs.append({
            "intent": {"name": "General Fallback", "description": f"A general post about {user_prompt}"},
            "framework": fallback_framework
        })
        used_framework_names.add(fallback_framework['name'])


    return {
        "is_persona_based": is_persona_based,
        "intent_framework_pairs": selected_intent_framework_pairs[:3], # Ensure we only return 3
        "intent_analysis": intent_analysis
    }

def generate_framework_reason(framework: Dict, intent: str, is_persona_based: bool, user_prompt: str) -> str:
    """Generate explanation for why this framework was chosen"""
    base_reasons = {
        "General & Direct Post": f"General & Direct framework chosen for a clear, on-point post that directly addresses the user's topic without unnecessary complexity.",
        "AIDA": f"AIDA framework chosen because the content needs to get attention and drive action, which fits the Attention-Interest-Desire-Action flow",
        "PAS": f"PAS framework picked to address the specific problem mentioned and give a clear solution",
        "Before-After-Bridge": f"Before-After-Bridge framework chosen to clearly show change and give useful steps",
        "Listicle": f"Listicle format picked to present information in an easy-to-read, useful way",
        "Question-led": f"Question-led approach chosen to get people engaged and encourage them to join the discussion",
        "Data-Driven Persuasion": f"Data-Driven Persuasion framework chosen to use compelling statistics to persuade and give clear action steps",
        "Credible Spotlight": f"Credible Spotlight framework picked to highlight real achievements while connecting to personal values and bigger missions",
        "Counterintuitive Leadership Truth": f"Counterintuitive Leadership Truth framework chosen to challenge common thinking and provide better leadership approaches"
    }

    reason = base_reasons.get(framework["name"], f"{framework['name']} framework chosen because it works well for sharing the intended message")

    # Add context based on intent and persona
    if intent == "Authority":
        reason += " to show expertise and build trust"
    elif intent == "Engagement" or intent == "Event":
        reason += " to get more people to interact and discuss"
    elif intent == "Branding":
        reason += " to strengthen brand identity and values"

    if is_persona_based:
        reason += ", including personal work context in a natural way"

    return reason

def create_framework_guidance(framework: Dict, tone_style: Dict, is_persona_based: bool, intent_analysis: Dict) -> str:
    """
    ***MODIFIED for GPT-Style***: Create concise, direct, and conversational framework guidance
    that mirrors the GPT example style.
    """

    intent_type = intent_analysis.get("intent_type", "general")

    # NEW: Master instruction for the desired style
    gpt_style_mandate = """
**GPT-STYLE MANDATE (CRITICAL):**
Your goal is to be **concise, direct, and use simple, natural English**.
- **Easy Language is Key:** Write in a way that anyone can easily understand. Avoid complex words and corporate jargon.
- **Get to the Point:** State the main idea in the first or second sentence.
- **Seamless Flow:** Do NOT use explicit labels like 'Problem:' or 'Attention:'. Weave the framework elements into a smooth, natural story.
- **Human Tone:** Write like a real person sharing an insight, not like a marketing template.
"""

    # Intent-specific context for framework guidance
    intent_context = ""
    if intent_type == "personal_story":
        intent_context = "\n**INTENT CONTEXT:** This is a personal story/journey post. Focus on authentic storytelling and personal growth narrative."
    elif intent_type == "personal_achievement":
        intent_context = "\n**INTENT CONTEXT:** This is about the user's own achievement. Highlight accomplishments while maintaining humility."
    elif intent_type == "third_party_spotlight":
        intent_context = "\n**INTENT CONTEXT:** This spotlights another person/company. Focus on their achievements while adding your perspective."
    elif intent_type == "advice":
        intent_context = "\n**INTENT CONTEXT:** This is advice/tips content. Structure as actionable insights for the audience."
    elif intent_type == "educational":
        intent_context = "\n**INTENT CONTEXT:** This is educational content. Focus on teaching and knowledge sharing."
    elif intent_type == "promotional":
        intent_context = "\n**INTENT CONTEXT:** This is promotional content. Balance promotion with value for the audience."

    guidance = f"""
FRAMEWORK: {framework['name']} - {framework['full_name']}
{gpt_style_mandate}{intent_context}
---
**FRAMEWORK-SPECIFIC GUIDANCE:**
"""

    # Add framework-specific guidance in the new "GPT Style"
    if framework["name"] == "General & Direct Post":
        guidance += """
- **Objective:** Announce news directly with energy and clarity.
- **Visual Structure:**
    1.  **The Announcement (Hook):** Start with the main point in a bolded sentence. e.g., `<p><strong>Big career update: I'm officially transitioning to a new role in MLOps! 🚀</strong></p>`
    2.  **The Details (Titled List):** In the center, introduce the key details with a bolded title, followed by a emoji list. This clearly outlines your focus.
        - e.g.,
          `<p><strong>My focus will be on:</strong></p>
          <ol>
              <li>⚙️ System Design & MLOps</li>
              <li>🧠 Full-Stack Deep Learning</li>
              <li>🏗️ Building clean, scalable infrastructure</li>
          </ol>`
    3.  **The Future (Quote Block):** End with a forward-looking statement or personal mission, framed in a `<blockquote>` to make it stand out as a key takeaway.
        - e.g., `<blockquote>I'm excited to help build the future of production-ready ML.</blockquote>`
"""
    elif framework["name"] == "AIDA":
        guidance += """
- **Objective:** Grab attention, build structured interest, create desire, and drive action.
- **Visual Structure:**
    - **Attention (Hook):** Start with a bold, exciting, and short hook.
        - e.g., `<p><strong>I believe the future of product management is changing.</strong></p>`

    - **Interest (Structured Block):** Build interest using the layered format from the image. This creates a mini-manifesto section.
        - **1. Bold Title:** Start with a bolded, all-caps title to frame the topic.
        - **2. Subtitle:** Add a short, declarative sentence right below it.
        - **3. Key Points:** List the core ideas in a bulleted list (`<ul>`).
        - e.g.,
          "<p><strong>THE BIG SHIFT:</strong> From Feature Factories to Value Engines</p>
          <p>It's no longer enough to just build features.</p>
          <ul>
              <li><strong>Understand Patterns:<strong> We need to deeply understand the 'why' behind every user problem.</li>
              <li><strong>Implement a Lifecycle:<strong> Ideas must move from discovery and validation to agile execution and clear measurement.</li>
          </ul>"

    - **Desire (Connecting Paragraph):** In a single, short paragraph, create desire by connecting the big ideas from the 'Interest' block to your personal news, skills, or goals. This makes the post personal.
        - e.g., `<p>That's why I'm so excited to step into a new role where I can focus on exactly that—using my experience in agile leadership to build products that deliver real, measurable value.</p>`

    - **Action (CTA):** End with a clear, single call-to-action in its own paragraph.
        - e.g., `<p>If you're also passionate about building products that deliver real value, I'd love to connect!</p>`
"""

    elif framework["name"] == "PAS":
        guidance += """
- **Objective:** Address a pain point, detail its negative impacts, and present a clear solution.
- **Visual Structure:**
    - **Problem (Hook):** State a common challenge in a bolded sentence.
        - e.g., `<p><strong>Too many development teams are stuck in endless feedback loops without shipping.</strong></p>`

    - **Agitate (Impact List):** Detail the negative consequences of the problem using the exact format below: a bolded title followed by a numbered emoji list.
        - e.g.,
          "<p><strong>This leads to a predictable set of problems:</strong></p>
          <ol>
              <li>⚠️ Developer burnout from constant rework.</li>
              <li>⏰ Key features get delayed, losing market advantage.</li>
              <li>🔗 A growing gap between the product and user needs.</li>
          </ol>"

    - **Solution (Header + Bulleted List):** Introduce your solution with a bolded header, then list the positive outcomes in a bulleted list.
        - e.g.,
          `<p><strong>This is where an agile development process makes all the difference:</strong></p>
          <ul>
              <li>It creates tight feedback loops that accelerate learning.</li>
              <li>It empowers teams to ship consistently and predictably.</li>
          </ul>`
"""
    elif framework["name"] == "Before-After-Bridge":
        guidance += """
- **Objective:** Illustrate a transformation by showing a journey from a past state to a better future state.
- **Visual Structure:**
    - **Before (Hook):** Describe the "old world" or initial state in a `<blockquote>`.
        - e.g., `<blockquote>"Previously, our data was scattered across siloed spreadsheets, making real-time analysis nearly impossible."</blockquote>`

    - **The 'After' (Key Outcomes):** In the center, describe the better "new world" by listing the key outcomes or responsibilities in a bulleted list (`<ul>`)
        - e.g.,
          `<ul>
              <li>Analyze business data to support decision-making</li>
              <li>Create and manage dashboards and reports</li>
              <li>Collaborate across departments to extract insights</li>
          </ul>`

    - **The Bridge (Connecting Phrase with Arrow):** Present your news or message as the bridge that makes the 'After' state possible. Use the arrow emoji (→) to make this transition clear.
        - e.g., `<p>That's why I'm thrilled to be stepping into my new role as a Data Analyst → to build a single source of truth and drive our business forward.</p>`
"""
    elif framework["name"] == "Listicle":
        guidance += """
- **Objective:** Share key insights in an easy-to-digest, high-value format.
- **Visual Structure:**
    1.  **The Setup (Hook):** A bolded hook that introduces the topic. e.g., `<p><strong>Here are 3 common mistakes to avoid in your next project plan.</strong></p>`
    2.  **The List (Titled Points):** Use an emoji-driven `<ol>` numbered list. Each `<li>` should be a two-part item: a **bolded title** followed by a brief, one-sentence explanation. This adds structure and scannability.
        - `<ol>
              <li><strong>🚀 Vague Objectives:</strong> Without a clear target, your team is flying blind.</li>
              <li><strong>⚖️ gnoring Scope Creep:</strong> Small additions snowball into major delays if not managed.</li>
              <li><strong>🗓️ Unrealistic Timelines:</strong> Ambition is great, but setting achievable deadlines is crucial for morale and success.</li>
          </ol>`
    3.  **The Close (CTA):** A concluding sentence that wraps it all up and asks a question.
"""
    elif framework["name"] == "Question-led":
        guidance += """
- **Objective:** Spark curiosity and engagement.
- **Visual Structure:**
    - **The Question (Hook):** Make the hook a bolded, thought-provoking question. Consider putting it in a `<blockquote>` to make it stand out. e.g., `<p><strong>What makes a product truly great—advanced tech or solving human problems?</strong></p>`
    - **The Insight (Bulleted List):** Provide your answer in a clear, scannable bulleted list. Each point should be a key part of your core belief.
        - `<ul>
             <li>It starts with deep empathy for the user's actual problem.</li>
             <li>Technology is a tool to serve that need, not the other way around.</li>
             <li>The best products are those that make users' lives better.</li>
          </ul>`
    - **The Connection (Final Paragraph):** In a final paragraph, connect that insight directly to your news or message.
"""
    elif framework["name"] == "Data-Driven Persuasion":
        guidance += """
- **Objective:** Use a compelling insight to make a point, then clearly list the implications.
- **Visual Structure:**
    - **The Stat (Hook):** Start with a bolded, surprising statistic or observation in a `<blockquote>` for maximum impact.
        - e.g., `<blockquote>"Did you know that nearly 70% of data science projects never make it into production?"</blockquote>`

    - **The 'So What?' (Implications List):** Explain why the statistic matters using the exact format below: a bolded title followed by a list.
        - e.g.,
          "<p><strong>Why this matters:</strong></p>
          <ul>
              <li>It's a huge waste of resources and talent.</li>
              <li>It shows a critical gap between R&D and real-world business impact.</li>
              <li>Companies are failing to capitalize on their biggest data assets.</li>
          </ul>"

    - **The Action (Final Paragraph):** Position your news, message, or solution as the direct answer to this problem.
        - e.g., `<p>That's why focusing on a robust MLOps strategy isn't just a 'nice-to-have'—it's the essential bridge to ROI.</p>`
"""
    elif framework["name"] == "Credible Spotlight":
        guidance += """
- **Objective:** Highlight key takeaways or expertise in a structured, easy-to-read list format.
- **Visual Structure:**
    - **Hook (Announcement):** Announce the topic or event directly. e.g., `<p><strong>Just finished an incredible match, and a few things really stood out.</strong></p>`
    - **The Key Points (Numbered & Titled List):** Introduce the list with a phrase, then use a numbered list (`<ol>`) with emojis. Each list item must have a **bolded title followed by a colon**, then the explanation. This matches the image format exactly.
        - e.g.,
          "<p>Here’s what stood out:</p>
          <ol>
              <li>🎯 <strong>Kuldeep Yadav's Spin Web:</strong> His 4-30 dismantled the middle order, showcasing the power of spin.</li>
              <li>🧘 <strong>Tilak Varma's Composure:</strong> His unbeaten 69 runs highlighted the importance of a cool head in high-pressure chases.</li>
              <li>🔄 <strong>Adaptable Strategy:</strong> The team's ability to adjust after losing early wickets demonstrates tactical flexibility.</li>
          </ol>"
    - **The Mission (Italicized Quote):** State your final thought or takeaway. e.g., `<em>"It's moments like these that remind you why strategy is just as important as skill."</em>`
"""
    elif framework["name"] == "Counterintuitive Leadership Truth":
        guidance += """
- **Objective:** Challenge a common belief by breaking down your reasoning into a clear, staged process.
- **Visual Structure:**
    - **The Contrarian Hook:** Start with a bold statement that goes against the grain. Use a `<blockquote>` to make it pop. e.g., `<blockquote>"The best leaders don't have all the answers. They create a system that finds them."</blockquote>`
    - **The "Why" (Staged Process):** In the center, explain your reasoning using a numbered list (`<ol>`). Each list item must start with a **bolded keyword followed by a colon**, just like below.
        - `<ol>
              <li><strong>Listen:</strong> True insights don't come from speaking; they come from actively listening to the team's unspoken challenges.</li>
              <li><strong>Filter:</strong> The goal isn't to add more process, but to filter out friction and make excellence the easiest path.</li>
              <li><strong>Inject:</strong> Great leaders inject trust and ownership into the team, rather than directives.</li>
              <li><strong>Refine:</strong> They constantly refine the system, empowering the team to find the best answers themselves.</li>
          </ol>`
    - **The Pivot (Final Paragraph):** Frame your news or message as your commitment to this counterintuitive belief in a final paragraph.
"""
        
    # This final part handles the specific logic for personal vs. general posts, which is still useful.
    # The new guidance is now much more direct and will produce the style you want.
    if is_persona_based:
        guidance = guidance.replace("the user's", "YOUR")
        guidance = guidance.replace("the user", "YOU")
        guidance = guidance.replace("people", "YOUR AUDIENCE")
    else:
        guidance = guidance.replace("YOUR", "the audience's")
        guidance = guidance.replace("YOU", "the user")
    
    return guidance


async def select_post_with_url_async(posts: List[str], query: str) -> Dict[str, Any]:
    """
    Select the best post to pair with a URL and fetch the URL asynchronously.
    This is the async version that should be used in async contexts.
    """
    try:
        posts_with_media = []
        for post in posts:
            media_analysis = analyze_post_for_media(post)
            posts_with_media.append({
                "content": post,
                "has_image": media_analysis["has_image"],
                "has_infographics": media_analysis["has_infographics"]
            })

        selected_index = select_best_post(posts_with_media)
        selected_post_content = posts[selected_index]

        # Generate search query from the selected post content
        search_query = generate_search_query_from_content(selected_post_content)

        # Fetch URL asynchronously
        url = await fetch_related_url(search_query)

        return {
            "selected_index": selected_index,
            "url": url
        }
    except Exception as e:
        print(f"Error selecting post with URL: {str(e)}")
        logging.error(f"Error in select_post_with_url_async: {e}")
        return {
            "selected_index": 0,
            "url": None
        }

def select_post_with_url(posts: List[str], query: str) -> Dict[str, Any]:
    """
    Select the best post to pair with a URL and fetch the URL.
    This function correctly handles the async call to fetch_related_url.
    """
    try:
        posts_with_media = []
        for post in posts:
            media_analysis = analyze_post_for_media(post)
            posts_with_media.append({
                "content": post,
                "has_image": media_analysis["has_image"],
                "has_infographics": media_analysis["has_infographics"]
            })

        selected_index = select_best_post(posts_with_media)
        selected_post_content = posts[selected_index]

        # Generate search query from the selected post content
        search_query = generate_search_query_from_content(selected_post_content)

        # Handle async URL fetching properly
        url = None
        try:
            # Check if we're in an async context
            loop = asyncio.get_running_loop()
            # We're in an async context, but this is a sync function
            # We need to create a task and run it in the existing loop
            import concurrent.futures
            import threading

            # Create a new event loop in a separate thread
            def run_async_in_thread():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(fetch_related_url(search_query))
                finally:
                    new_loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_async_in_thread)
                url = future.result(timeout=30)  # 30 second timeout

        except RuntimeError:
            # No running loop; safe to use asyncio.run
            url = asyncio.run(fetch_related_url(search_query))
        except Exception as e:
            logging.error(f"Error fetching URL: {e}")
            url = None

        return {
            "selected_index": selected_index,
            "url": url
        }
    except Exception as e:
        print(f"Error selecting post with URL: {str(e)}")
        logging.error(f"Error in select_post_with_url: {e}")
        return {
            "selected_index": 0,
            "url": None
        }

def is_url_necessary_for_posts(posts_content: List[str]) -> bool:
    """
    Analyzes the content of generated posts to determine if adding a URL is necessary.
    A URL is necessary for topics that are news-driven, data-heavy, or reference specific external events.
    It is NOT necessary for general advice, personal stories, or evergreen content.
    """
    from app.utils.model_initializer import model
    import logging

    # Combine content for a single analysis call
    combined_content = "\n\n---POST SEPARATOR---\n\n".join(posts_content)

    analysis_prompt = f"""
    You are an expert content strategist. Your task is to determine if adding a source URL would genuinely enhance a set of LinkedIn posts. Analyze the combined content of the three post variants below.

    A URL is NECESSARY and valuable if the posts discuss:
    - Specific, recent news events (e.g., a company's product launch, a new political development, a recent scientific discovery).
    - Hard data, statistics, or the results of a study that would benefit from a source link for credibility.
    - A specific article, report, or external resource that is being commented on.

    A URL is NOT NECESSARY if the posts discuss:
    - General advice or "how-to" tips (e.g., "5 tips for better leadership").
    - A personal story or reflection (e.g., "My journey into tech").
    - Evergreen or philosophical topics (e.g., "The importance of teamwork").
    - A simple personal announcement (e.g., "I'm excited to start my new job").

    Here is the content of the posts:
    ---
    {combined_content}
    ---

    Based on this analysis, is a source URL necessary to add credibility or context to these posts?
    Respond with ONLY a JSON object in the following format:

    {{
        "url_is_necessary": boolean
    }}
    """
    try:
        response = model.generate_content(analysis_prompt, use_cache=False)
        analysis_text = response.text.strip()

        # Clean up JSON response
        if "```json" in analysis_text:
            parts = analysis_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    analysis_text = sub_parts[0].strip()

        import json
        result = json.loads(analysis_text)
        return result.get("url_is_necessary", False)

    except Exception as e:
        logging.error(f"Error determining if URL is necessary: {e}")
        # Default to False to be conservative and not add unnecessary URLs
        return False


@lru_cache(maxsize=500)
def analyze_post_for_media(post_content: str) -> dict:
    """Analyze a post to determine if it would benefit from images or infographics.
    
    Args:
        post_content: The content of the post
        
    Returns:
        Dictionary with has_image and has_infographics flags
    """
    try:
        # Simple heuristic-based analysis
        content_lower = post_content.lower()
        
        # Check for data/statistics indicators (more specific)
        data_indicators = [
            'percent', '%', 'statistics', 'data', 'comparison', 'chart', 'graph',
            'numbers', 'figures', 'analysis', 'survey', 'study', 'research',
            'increase', 'decrease', 'growth', 'decline', 'trend', 'survey results',
            'market share', 'revenue', 'profit', 'cost', 'efficiency', 'performance metrics'
        ]
        
        # Check for visual content indicators (more specific)
        visual_indicators = [
            'image', 'photo', 'picture', 'visual', 'design', 'layout',
            'color', 'brand', 'logo', 'icon', 'illustration', 'screenshot',
            'before and after', 'comparison image', 'product image', 'team photo'
        ]
        
        # Check for process/step indicators (more specific)
        process_indicators = [
            'step', 'process', 'workflow', 'pipeline', 'framework',
            'methodology', 'approach', 'strategy', 'plan', 'roadmap',
            'timeline', 'phases', 'stages', 'cycle', 'lifecycle'
        ]
        
        # Count occurrences to determine strength of indicators
        data_count = sum(1 for indicator in data_indicators if indicator in content_lower)
        visual_count = sum(1 for indicator in visual_indicators if indicator in content_lower)
        process_count = sum(1 for indicator in process_indicators if indicator in content_lower)
        
        # Determine media recommendations with more nuanced logic
        has_strong_data = data_count >= 3  # Increased threshold - need more data indicators
        has_strong_visual = visual_count >= 2  # Need multiple visual indicators
        has_strong_process = process_count >= 3  # Increased threshold - need more process indicators
        
        # Content length consideration
        content_length = len(post_content)
        is_long_content = content_length > 1000
        
        # Determine media type based on content analysis
        has_infographics = False
        has_image = False
        
        # Priority 1: Strong data indicators suggest infographics
        if has_strong_data:
            has_infographics = True
        # Priority 2: Strong visual indicators suggest images
        elif has_strong_visual:
            has_image = True
        # Priority 3: Strong process indicators suggest infographics
        elif has_strong_process:
            has_infographics = True
        # Priority 4: Long content without specific indicators might benefit from images
        elif is_long_content and not (has_strong_data or has_strong_visual or has_strong_process):
            has_image = True
        # Priority 5: Only consider infographics if we have multiple data indicators (at least 2)
        elif data_count >= 2 and not has_strong_visual and not has_strong_process:
            has_infographics = True
        # Priority 6: If we have some visual indicators but not strong enough, still consider images
        elif visual_count >= 1 and not has_strong_data and not has_strong_process:
            has_image = True
        
        # Ensure mutual exclusivity
        if has_infographics and has_image:
            # If both are true, prioritize based on content type
            if has_strong_data or has_strong_process:
                has_image = False
                has_infographics = True
            elif has_strong_visual:
                has_image = True
                has_infographics = False
            else:
                # Default to infographics for data-heavy content
                has_image = False
                has_infographics = True
        
        return {
            "has_image": has_image,
            "has_infographics": has_infographics
        }
    except Exception as e:
        print(f"Error analyzing post for media: {str(e)}")
        return {
            "has_image": False,
            "has_infographics": False
        }

def select_best_post(posts_with_media):
    """Select the best post from a list of posts with media analysis.
    
    Args:
        posts_with_media: List of dictionaries with post content and media analysis
        
    Returns:
        Index of the best post
    """
    try:
        # Simple selection logic - prefer posts with infographics, then images
        best_index = 0
        best_score = 0
        
        for i, post_data in enumerate(posts_with_media):
            score = 0
            if post_data.get("has_infographics"):
                score += 3
            if post_data.get("has_image"):
                score += 2
            if len(post_data.get("content", "")) > 1000:
                score += 1
                
            if score > best_score:
                best_score = score
                best_index = i
                
        return best_index
    except Exception as e:
        print(f"Error selecting best post: {str(e)}")
        return 0

async def add_url_to_best_post_async(response_posts: List[Dict], user_prompt: str = "") -> List[Dict]:
    """
    Asynchronously analyzes if a URL is needed, and if so, adds it to the best post.
    """
    if not response_posts:
        return response_posts

    try:
        final_post_contents = [post["content"] for post in response_posts]

        # --- NEW DECISION GATE ---
        # First, determine if any post actually needs a URL by calling our new function.
        if not is_url_necessary_for_posts(final_post_contents):
            logging.info("URL attachment deemed unnecessary for this set of posts.")
            return response_posts

        # --- EXISTING LOGIC ---
        # This code will now only run if the above check passes.
        logging.info("URL attachment is necessary. Proceeding to find the best post and fetch a URL.")
        url_result = await select_post_with_url_async(final_post_contents, user_prompt or "")

        if url_result.get("url"):
            best_post_index = url_result.get("selected_index", 0)
            if best_post_index < len(response_posts):
                response_posts[best_post_index]["url"] = url_result["url"]
                logging.info(f"Added URL to post {best_post_index + 1}: {url_result['url']}")
        else:
            logging.warning("URL was deemed necessary, but no relevant URL was fetched.")

    except Exception as e:
        logging.error(f"Error adding URL to best post: {e}")

    return response_posts

def generate_post_from_persona_keywords(general_persona_keywords, tone, style, user_prompt, content_interests=None, network_interests=None, add_emojis=False, add_hashtags=True, use_hook_generator=True, used_opening_words=None, include_tone_in_response=False, length=None):
    """
    ***MODIFIED***: This version now includes real-time search for recent events using Tavily.
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting post generation with multi-intent and real-time search system")
    
    if used_opening_words is None:
        used_opening_words = set()

    # --- NEW SEARCH LOGIC ---
    # Step 1: Analyze the prompt to see if it requires a real-time web search.
    logger.info("Analyzing prompt for recency...")
    recency_analysis = analyze_prompt_for_recency(user_prompt)
    search_context = None

    if recency_analysis.get("requires_search"):
        query = recency_analysis.get("search_query")
        logger.info(f"Prompt requires search. Querying Tavily with: '{query}'")
        search_context = perform_tavily_search(query)
        if search_context:
            logger.info("Successfully retrieved up-to-date search context.")
        else:
            logger.warning("Failed to retrieve search context, proceeding without it.")
    else:
        logger.info("Prompt does not require a real-time search.")
    
 # <<< --- STEP 1: DEFINE THE PERSONA MANDATE AS A VARIABLE --- >>>
    persona_mandate_text = """
---
### 🧠 THE CRITICAL PERSONA MANDATE: You Are NOT a News Reporter

**Your most important task is to adopt the persona of a thoughtful professional sharing their perspective on an event, NOT a journalist breaking the news.**

**Mental Model:** Imagine you just saw a headline on the news. You are now turning to your LinkedIn network to share your *personal perspective* or *professional analysis* on it. Your audience likely already knows the basic facts; they are following you for your *insight*.

**1. Assume the Facts, Don't Report Them:**
   - You are FORBIDDEN from acting like a journalist who is breaking a story. Do not attribute facts to external sources.
   - State the key facts of the event confidently and directly in your own voice as the foundation for your analysis.

**2. Prohibited Language (This is a strict rule):**
   - You MUST NOT use phrases that attribute information, such as:
     - `Reports indicate...`
     - `News outlets report...`
     - `According to sources...`
     - `It has been reported that...`

**3. The Correct Approach (State the Fact, Then Add Your Take):**
   - **INSTEAD OF (Forbidden):** "Reports indicate that former Senator Mushtaq Ahmed Khan was arrested."
   - **DO THIS (Correct):** **"The arrest of former Senator Mushtaq Ahmed Khan is a significant and concerning development in the ongoing Gaza aid efforts."** (This states the fact as a known event and immediately adds a perspective).

   - **INSTEAD OF (Forbidden):** "News outlets report the flotilla was intercepted by Israeli forces."
   - **DO THIS (Correct):** **"When Israeli forces intercepted the Gaza-bound flotilla, it immediately escalated a humanitarian issue into a major diplomatic incident."** (This weaves the fact directly into an analytical narrative).

This mandate is the strategic "why" behind the "How to Humanize Data" rule. Your primary function is to provide a unique, human perspective, not to be a news aggregator.
---
"""

     # --- INTELLIGENT CONTEXT ANALYSIS ---
    # FIXED: This now returns the intent_framework_pairs which is the core of the new logic.
    framework_analysis = classify_prompt_and_select_frameworks(user_prompt, general_persona_keywords)
    intent_framework_pairs = framework_analysis["intent_framework_pairs"]
    is_persona_based = framework_analysis["is_persona_based"]
    intent_analysis = framework_analysis.get("intent_analysis", {})
    
    content_complexity = intent_analysis.get("content_complexity", "direct_announcement")
    sentiment = intent_analysis.get("sentiment", "neutral")
    persona_focus = intent_analysis.get("persona_focus", "none")

    # --- CRITICAL FIX FOR GENERIC PROMPTS ---
    # If no prompt is provided, force the complexity to be short and direct.
    if not user_prompt:
        logger.info("No user prompt provided. Forcing 'direct_announcement' complexity for generic post.")
        intent_analysis['content_complexity'] = 'direct_announcement'
    
    content_complexity = intent_analysis.get("content_complexity", "direct_announcement")
    sentiment = intent_analysis.get("sentiment", "neutral")

    # Check if user provided tone, style, or length parameters
    user_provided_params = {
        'tone': tone,
        'style': style,
        'length': length
    }
    use_user_params = any(param is not None for param in user_provided_params.values())

    if use_user_params:
        logger.info(f"Using user-provided parameters: tone={tone}, style={style}, length={length}")
        # When user provides parameters, apply them to all frameworks
        for pair in intent_framework_pairs:
            framework = pair['framework']

            # Build comprehensive user parameter application
            updated_framework = {}

            if tone:
                updated_framework.update({
                    "assigned_tone": tone,
                    "tone_description": f"User-specified {tone} tone - MUST be applied to entire post",
                    "voice_characteristics": f"{tone} voice throughout the post",
                    "framework_tone_synergy": f"User-specified {tone} tone applied to {framework['name']} framework"
                })

            if style:
                # Always apply style if provided, regardless of whether tone is also provided
                style_attributes = [style] if isinstance(style, str) else style
                updated_framework.update({
                    "style_attributes": style_attributes,
                })

                # Update approach guidance to include both tone and style
                approach_parts = []
                if tone:
                    approach_parts.append(f"Use {tone} tone")
                if style:
                    approach_parts.append(f"follow {style} writing style")

                updated_framework["approach_guidance"] = " and ".join(approach_parts) if approach_parts else framework.get("approach_guidance", "")

            elif tone and not style:
                # If only tone is provided, keep existing style attributes
                updated_framework.update({
                    "style_attributes": framework.get("style_attributes", []),
                    "approach_guidance": f"Use {tone} tone"
                })

            # Apply all updates to the framework
            framework.update(updated_framework)
    else:
        # FIXED: Correctly extract the frameworks from the pairs to send for tone assignment.
        selected_frameworks_for_tone = [pair['framework'] for pair in intent_framework_pairs]
        try:
            frameworks_with_tones = intelligent_tone_assignment(selected_frameworks_for_tone, user_prompt, intent_analysis)
            # Re-integrate the assigned tones back into our pairs
            for i, pair in enumerate(intent_framework_pairs):
                pair['framework'].update(frameworks_with_tones[i])
            logger.info(f"Applied intelligent tone assignment.")
        except Exception as e:
            logger.warning(f"Error in tone assignment, using fallback: {str(e)}")

    # Generate 3 distinctly different variant posts
    def generate_variant(i):
        # FIXED: Correctly get the data for each variant from the intent_framework_pairs list.
        pair = intent_framework_pairs[i]
        framework = pair['framework']
        specific_intent = pair['intent'] # The specific intent for this variant
        
        logger.info(f"Generating variant {i+1} for intent '{specific_intent['name']}' using framework '{framework['name']}'")

        assigned_tone_style = {
            "tone": framework.get("assigned_tone", "Professional"),
            "style": ", ".join(framework.get("style_attributes", [])),
            "approach": framework.get("approach_guidance", ""),
            "voice": framework.get("voice_characteristics", ""),
            "description": framework.get("tone_description", "")
        }
        
        # FIXED: The reason is now much more specific, explaining WHY this framework was chosen for this specific intent.
        framework_reason = f"{framework['name']} framework was chosen because it's best suited to address the user's potential intent of: '{specific_intent['name']}'."
        framework_guidance = create_framework_guidance(framework, assigned_tone_style, is_persona_based, intent_analysis)
       
        # *** NEW: Dynamic Persona Instructions based on persona_focus ***
        persona_instructions = ""
        if persona_focus == "deep":
            persona_instructions = f"""
---
### 👤 PERSONA CONTEXT: DEEP FOCUS (High-Level)

-   **Your Primary Task:** This is a personal story. Your goal is to provide just enough context to make the story credible, **not to list a résumé.**
-   **CRITICAL RULE:** From the provided keywords (`{", ".join(general_persona_keywords)}`), you MUST select **ONLY 1-2 of the most relevant ones** to mention briefly and naturally.
-   **EXAMPLE:** Instead of listing every skill, you might say "...after my journey in software development..." or "...leveraging my background in product leadership...".
-   **AVOID:** Do NOT list multiple technologies, methodologies, and years of experience all in one paragraph. Keep it high-level and human.
"""
        elif persona_focus == "contextual":
            persona_instructions = f"""
---
### 👤 PERSONA CONTEXT: CONTEXTUAL FOCUS (High-Level)

-   **Your Primary Task:** Write this post in the user's voice ("I"). The topic is the main subject.
-   **CRITICAL RULE:** You may use **at most ONE** relevant keyword from the user's background (`{", ".join(general_persona_keywords)}`) IF it adds essential context. Otherwise, do not mention it.
-   **AVOID:** Do not force persona keywords into the post. The focus should be on the announcement or topic, not the user's entire work history.
"""
        else: # persona_focus == "none"
            persona_instructions = "\n-   **Persona Context:** NOT APPLICABLE. Write from a general, objective professional perspective."
       
        if i > 0 and persona_focus != "none":
            persona_instructions += "\n-   **VARIETY RULE:** Try to select different keywords or angles from the user's background than the ones you might have used for other variants."

        # --- DYNAMIC LENGTH INSTRUCTION (USER-PROVIDED OR AUTOMATIC) ---
        # Using the EXACT same length instructions as content_modifier.py for consistency
        length_instruction = ""
        if length:
            # User provided a specific length preference (case-insensitive)
            length_lower = length.lower()

            # Extract length guidance from content_modifier.py logic - EXACT COPY
            length_guidance = ""
            if length_lower == "short":
                length_guidance = "This is a **CRITICAL** instruction. The post **MUST BE SHORT** (under 600 characters, target 400-500 characters for safety). Be extremely concise. The Hook must be a single, powerful sentence. The Body should be 2-3 sentences max. **CRITICAL**: Stay well under 550 characters to ensure strict compliance."
            elif length_lower == "medium":
                length_guidance = """
This is a **CRITICAL** instruction. The post must be **MEDIUM** length (under 1500 characters). Provide a good balance of detail and readability.
-   **Paragraph Structure (CRITICAL):** Do NOT use dense paragraphs. Keep most paragraphs to 2-3 sentences. Use single-sentence paragraphs for emphasis. The goal is a scannable, visually appealing post.
-   **Body:** The Body should be well-developed with clear paragraphs and/or a bulleted list.
"""
            elif length_lower == "long":
                length_guidance = """
This is a **CRITICAL** instruction. The post must be a **LONG**, in-depth piece (above 1500 characters and under 2,500 characters).
-   **Character Limit (ABSOLUTE RULE):** The final post, including all HTML and text, MUST NOT exceed 3,000 characters. This is the maximum length for a LinkedIn post.
-   **Paragraph Structure (CRITICAL):** It is absolutely essential to avoid dense paragraphs. The post MUST be highly scannable on the LinkedIn feed.
    -   Keep paragraphs short (2-3 sentences max).
    -   Use one-sentence lines to break up text and add impact.
    -   Utilize whitespace effectively to guide the reader's eye.
-   **Content Goal:** The post must be comprehensive and establish expertise, but its formatting must prioritize readability.
"""

            # Format the length instruction using content_modifier.py structure with strict enforcement
            length_instruction = f"""
---
### 📏 LENGTH GUIDANCE (CRITICAL) - USER-SPECIFIED

**Target Length:** {length.capitalize()}
**Instructions:** {length_guidance}

**🚨 CRITICAL LENGTH ENFORCEMENT - HIGHEST PRIORITY 🚨**
**THESE LENGTH REQUIREMENTS OVERRIDE ALL OTHER INSTRUCTIONS AND MUST BE FOLLOWED EXACTLY:**

📐 **MANDATORY LENGTH: {length.upper()}**

**ABSOLUTE REQUIREMENTS:**
- The user has specified a **{length}** length for this post
- You MUST generate content that strictly adheres to the **{length}** length specifications above
- This is NOT optional - it is a CRITICAL requirement that overrides all other length suggestions
- ALL 3 variants must have consistent **{length}** length - no mixing of short/medium/long across variants

**LENGTH IMPLEMENTATION MANDATE:**
- Follow the exact character count ranges specified above for **{length}** posts
- Count characters carefully (excluding HTML tags) and ensure you stay STRICTLY within the specified range
- If you're generating content that doesn't match the **{length}** specifications, adjust immediately
- The reader should immediately recognize this as a **{length}** post based on its length
- **CRITICAL**: For SHORT posts, stay under 550 characters (100 char safety margin)
- **CRITICAL**: For MEDIUM posts, stay under 1450 characters (50 char safety margin)
- **CRITICAL**: For LONG posts, stay between 1500-2400 characters (100 char safety margin)

**MANDATORY FINAL LENGTH CHECK:**
Before submitting your response, verify:
1. Length check: Does the post content match the **{length}** character count requirements specified above?
2. Range check: Are you STRICTLY within the character limits (not just close to them)?
3. Consistency check: Is this variant the same **{length}** as the other variants being generated?
4. If ANY aspect fails these checks, REWRITE it immediately to comply with **{length}** specifications.

**FAILURE TO MEET THESE USER-SPECIFIED LENGTH REQUIREMENTS WILL RESULT IN REJECTION.**
---
"""
        else:
            # Use automatic length based on content complexity
            if content_complexity == "direct_announcement":
                length_instruction = """
---
### 📏 POST LENGTH & STYLE: SHORT & DIRECT

**CRITICAL STYLE NOTE:** Keep the post very short and to the point (approx. 3-5 sentences). Focus on making a clear, confident announcement. Prioritize brevity and scannability above detailed strategic arguments. The goal is a quick, authentic update.
"""
            else: # "strategic_narrative"
                length_instruction = """
---
### 📏 POST LENGTH & STYLE: STRATEGIC & DETAILED

**STYLE NOTE:** Create a well-structured, persuasive post. It **MUST** have a well-developed body of at least **2-3 short paragraphs** between the opening hook and the final call-to-action. You are required to elaborate on the core idea using the chosen framework to build a compelling narrative or argument. The goal is maximum impact and persuasion. A post with only one or two sentences in the body is a **FAILED** task for this complexity level.
"""

# --- NEW: THE NEGATIVE PROMPT REFRAMING GUARDRAIL ---
        reframing_instruction = ""
        if sentiment == 'negative':
            reframing_instruction = """
---
### ⚠️ SPECIAL HANDLING INSTRUCTIONS: NEGATIVE PROMPT DETECTED

The user's prompt is unprofessional and negative. Your primary task is to **reframe** this raw emotion into a constructive and professional LinkedIn post.

1.  **DO NOT REPEAT THE NEGATIVE LANGUAGE:** Absolutely do not use words like "hate," "bossy," "sucks," or any insults from the user's prompt.
2.  **IDENTIFY THE UNDERLYING PROFESSIONAL THEME:** Analyze the user's frustration and extract the core professional topic. For example:
    -   "I hate my boss" -> The theme is "the importance of good leadership" or "navigating difficult management styles."
    -   "My company sucks" -> The theme is "the key elements of a positive work culture" or "seeking a new environment that values its employees."
3.  **WRITE ABOUT THE REFRAMED THEME:** Your entire post must be about the new, constructive theme you identified. Frame it as a thoughtful reflection, a piece of advice for others, or an insight into what makes a great workplace.
4.  **MAINTAIN A PROFESSIONAL TONE:** The final output must be 100% professional and suitable for a broad LinkedIn audience.
"""

# *** THIS IS THE FIX: Select the specific hook instruction for the current variant ***
        hook_instructions = [
    """-   **Variant 1 Hook - The 'Problem/Thematic' Frame:**
    -   **Task:** For most prompts, announce the event directly and tie it to a core theme (e.g., "Celebrating X and thinking about Y...").
    -   **Special Rule for Recommendations:** Frame the need around the **problem** it solves. Start with the pain point.
    -   **Example:** "That sinking feeling when a pipe bursts on a weekend is the worst."
    -   **UNIQUENESS DIRECTIVE:** This is the foundational, direct hook. Be clear and thematic.""",

    """-   **Variant 2 Hook - The 'Person/Reflective' Frame:**
    -   **UNIQUENESS DIRECTIVE (CRITICAL):** This hook **MUST NOT** be a direct, thematic announcement like the first variant. It must start from a human-centric or reflective angle.
    -   **Task:** Focus on the meaning or history behind the event (e.g., "On X, I'm always reminded of Y...").
    -   **Special Rule for Recommendations:** Instead of history, reflect on what makes a professional truly **great**. Focus on the qualities of the person.
    -   **Example:** "It's rare to find a professional who is not only skilled but also incredibly reliable and fair." """,

    """-   **Variant 3 Hook - The 'Value/Forward-Looking' Frame:**
    -   **UNIQUENESS DIRECTIVE (CRITICAL):** This hook must be conceptually different from both a thematic announcement (Variant 1) and a personal reflection (Variant 2). Focus on the 'so what?' or 'what's next?'.
    -   **Task:** Connect the event to a future goal or opportunity (e.g., "May X inspire us to do Y...").
    -   **Special Rule for Recommendations:** Focus on the future **value** or peace of mind the recommendation provides. Frame it around the positive outcome.
    -   **Example:** "The peace of mind that comes from having a go-to expert for home emergencies is priceless." """
]
        specific_hook_instruction = hook_instructions[i]

        # This preamble defines the core rule of ensuring only ONE CTA exists.
        cta_preamble = """**CRITICAL CTA MANDATE:** Your primary goal is to ensure the post has **exactly one** natural, contextually appropriate call-to-action (CTA).

**FORMATTING RULE (NON-NEGOTIABLE):**
- The CTA **MUST** be the very last paragraph of the post content (before the hashtags).
- It **MUST** be a standalone paragraph, wrapped in its own `<p>` tags. This creates a clean line break.
- The CTA should almost always be a question designed to drive engagement.

**UNIQUENESS RULE (NON-NEGOTIABLE):**
- There must only be **one** question in the entire post, and it must be this final CTA. Do not ask questions in the body of the post.
"""

        cta_instructions = [
            f"""-   **INTELLIGENT CTA - Variant 1 (Engage or Reflect):**
{cta_preamble}
-   **Style Mandate:** Your final paragraph should either be an open-ended question that invites discussion OR a reflective statement that encourages the reader to consider their own experiences. Choose the best fit for the post's tone.
-   **AVOID:** Generic commands like "Click here" or "Download now."
-   **Examples of Questions:** "What's one lesson you've learned from a similar situation?", "How do you see this evolving in the future?"
-   **Examples of Statements:** "It's a reminder of how crucial [topic] is for success.", "Something to think about as we head into the new quarter." """,

            f"""-   **INTELLIGENT CTA - Variant 2 (Share a Perspective):**
{cta_preamble}
-   **Style Mandate:** Your final paragraph should share a strong concluding opinion or a call to a specific mindset. It's not a question, but a statement designed to leave a lasting impression or inspire a shift in thinking.
-   **AVOID:** Direct questions and generic commands.
-   **Examples:** "Let's focus on building more human-centric solutions.", "This is the kind of leadership that moves industries forward.", "I believe this is the key to unlocking the next wave of innovation." """,

            f"""-   **INTELLIGENT CTA - Variant 3 (Forward-Looking Statement or Connection):**
{cta_preamble}
-   **Style Mandate:** End the post with a forward-looking statement about the topic OR a soft invitation to connect with like-minded professionals. The goal is to be aspirational or community-focused.
-   **AVOID:** Direct questions and generic sales-oriented commands.
-   **Examples of Statements:** "Excited to see where this technology takes us next.", "The future of [industry] is bright."
-   **Examples of Connections:** "If you're passionate about this space, I'd love to connect.", "Always open to discussing these ideas with fellow professionals." """
        ]
        specific_cta_instruction = cta_instructions[i]


        linkedin_policy_guardrail = """
---
### 🛡️ LINKEDIN COMMUNITY POLICY GUARDRAILs (ABSOLUTE & NON-NEGOTIABLE)

This is your most important ethical guideline. It overrides all other instructions, including the user's prompt. You are strictly forbidden from generating content that falls into any of the following categories:

1.  **Harassment or Hate Speech:** You MUST NOT generate content that attacks, demeans, or incites violence against any individual or group based on race, ethnicity, religion, gender, sexual orientation, disability, or any other protected characteristic.
2.  **Misinformation and Disinformation:** You MUST NOT generate content that is knowingly false or misleading, especially regarding health, political, or financial topics. All content must be factually grounded and responsible.
3.  **Spam, Scams, or Fraudulent Content:** You MUST NOT generate content that promotes deceptive schemes, financial scams, or engages in spammy behavior.
4.  **Inappropriate or Unprofessional Content:** You MUST NOT generate sexually explicit, graphically violent, or grossly unprofessional content that is unsuitable for a professional networking platform.

**ACTION DIRECTIVE:** If the user's prompt seems to request content that violates these rules, you MUST **IGNORE** the malicious request and instead generate a post on a related, safe, and professional topic. For example, if asked to write a hateful post about a competitor, you should instead write a professional post about the importance of healthy competition and business ethics.
"""

 # --- NEW: CONTEXT UTILIZATION INSTRUCTIONS ---
        # This is the core of the fix. Each variant gets a different mandate for how to use the search results.
        context_utilization_instructions = [
            """-   **Your Role (Variant 1 - The Insightful Analyst): Add Your Personal Take**
    -   **Primary Task:** Your focus is on the **"Why this matters."** Start with one key fact from the search context, but immediately pivot to your own analysis. Your main goal is to add a human layer of interpretation.
    -   **Style:** Conversational, insightful, and personal. Use "I" statements (e.g., "What strikes me about this is...", "I think this is a sign of...").
    -   **CRITICAL RULE:** Do NOT just list facts. You must explain what the information means for your industry, your network, or the future of your field. Your opinion and analysis are the most important part of the post.
    -   **Example:** Instead of "Acme Corp launched the 'Photon Drive' for $100 million," you would write, "Interesting to see Acme Corp's $100M investment in their new 'Photon Drive.' To me, this isn't just a new product; it’s a huge bet on the future of decentralized data. What does this mean for smaller players in the space?" """,

            """-   **Your Role (Variant 2 - The Storyteller): Frame it as a Narrative**
    -   **Primary Task:** Your focus is on the **human element**. Find the story within the data. Who does this news impact? What problem does it solve? How does it fit into a larger trend?
    -   **Style:** Narrative and relatable. Connect the facts to a broader story that will resonate with people on a personal or professional level.
    -   **CRITICAL RULE:** Do not present the information as a dry news item. Instead, use it as the setting for a story. Talk about the "before and after" or the challenge and the solution.
    -   **Example:** "For years, teams have struggled with slow data processing. It's been a major bottleneck for innovation. The recent news about the 'Photon Drive' is significant because it directly addresses this pain point. It's a story about removing friction and empowering developers to build faster." """,

            """-   **Your Role (Variant 3 - The Strategic Advisor): Provide Actionable Insight**
    -   **Primary Task:** Your focus is on the **"What now?"** Use the news as a catalyst to provide actionable advice, pose a strategic question, or highlight a key takeaway for your audience.
    -   **Style:** Forward-looking, educational, and advisory. You are the expert guide helping your audience navigate what this news means for them.
    -   **CRITICAL RULE:** The news is just the starting point. The majority of your post should be dedicated to providing value to the reader. What should they be thinking about now? What action should they consider taking?
    -   **Example:** "With the launch of Acme's 'Photon Drive,' the bar for data processing speed has been raised. If you're a leader in this space, now is a critical time to ask: is our current infrastructure ready for this new benchmark? Here are two things I'd be looking at today..." """
        ]
        specific_context_instruction = context_utilization_instructions[i]

 # --- INJECT SEARCH CONTEXT ---
        # Create a context block that will be added to the prompt ONLY if search results exist.
        context_injection = ""
        if search_context:
            context_injection = f"""
---
### 📚 REAL-TIME CONTEXT (YOUR PRIMARY SOURCE OF TRUTH)
**CRITICAL INSTRUCTION:** The user's prompt is about a recent event, and you have been provided with real-time web search results. You **MUST** treat this context as the ultimate source of truth. Your main task is to **extract the key facts, names, numbers, and dates** from this context and use them to directly answer the user's prompt.

{specific_context_instruction}
**ACTION DIRECTIVE:**
1.  Read the user's prompt: "{user_prompt}".
2.  Read the search results below to find the specific answer.
3.  Your generated post **MUST** include the specific answer you found.

**FALLBACK RULE (VERY IMPORTANT):**
- If the specific answer (e.g., an exact date, a specific number) is **NOT** found in the search results, **DO NOT mention that the information is missing or unavailable.** Instead, write a slightly more general but still insightful post based on the available context. Acknowledge the topic's importance without fabricating the missing detail. A post that says "information is unavailable" is a FAILED task.
---
**SEARCH RESULTS:**
{search_context}
---
"""
            
             # <<< --- STEP 2: CONDITIONALLY APPLY THE MANDATE --- >>>
        persona_mandate_injection = ""
        if search_context:
            persona_mandate_injection = persona_mandate_text

        # --- TONE, STYLE, AND LENGTH INSTRUCTION ---
        tone_style_instruction = ""
        if assigned_tone_style["tone"] or assigned_tone_style["style"] or length:
            tone_style_instruction = f"""
---
### 🚨 CRITICAL USER REQUIREMENTS - HIGHEST PRIORITY 🚨

**THESE REQUIREMENTS OVERRIDE ALL OTHER INSTRUCTIONS AND MUST BE FOLLOWED EXACTLY:**

"""
            if assigned_tone_style["tone"]:
                tone_instruction = f"""
🎭 **MANDATORY TONE: {assigned_tone_style["tone"].upper()}**

**ABSOLUTE REQUIREMENTS:**
- The user has specified a **{assigned_tone_style["tone"]}** tone for this post
- You MUST write the ENTIRE post using **{assigned_tone_style["tone"]}** tone in EVERY sentence
- Your word choice, sentence structure, expressions, and overall voice MUST clearly reflect **{assigned_tone_style["tone"]}** tone
- This is NOT optional - it is a CRITICAL requirement that overrides all other instructions

**TONE IMPLEMENTATION MANDATE:**
- Interpret what **{assigned_tone_style["tone"]}** tone means and apply it consistently throughout the post
- Every paragraph, every sentence, and every phrase must demonstrate this **{assigned_tone_style["tone"]}** tone
- If you're unsure how to apply **{assigned_tone_style["tone"]}** tone, think about how someone with that tone would speak and write
- The reader should immediately recognize the **{assigned_tone_style["tone"]}** tone from the very first sentence
"""

                tone_style_instruction += tone_instruction

            if assigned_tone_style["style"]:
                style_instruction = f"""

🎨 **MANDATORY STYLE: {assigned_tone_style["style"].upper()}**

**ABSOLUTE REQUIREMENTS:**
- The user has specified a **{assigned_tone_style["style"]}** writing style for this post
- You MUST structure and present the ENTIRE content following **{assigned_tone_style["style"]}** writing style
- This affects how you organize information, present ideas, and structure the narrative flow
- This is NOT optional - it is a CRITICAL requirement that overrides all other style instructions

**STYLE IMPLEMENTATION MANDATE:**
- Interpret what **{assigned_tone_style["style"]}** writing style means and apply it consistently throughout the post
- The overall structure, organization, and presentation method must clearly reflect **{assigned_tone_style["style"]}** style
- If you're unsure how to apply **{assigned_tone_style["style"]}** style, think about how content in that style is typically written and structured
- The reader should immediately recognize the **{assigned_tone_style["style"]}** writing style from how the content is organized and presented
"""

                tone_style_instruction += style_instruction

            # Add length enforcement if user provided length parameter
            if length:
                length_enforcement_instruction = f"""

📐 **MANDATORY LENGTH: {length.upper()}**

**ABSOLUTE REQUIREMENTS:**
- The user has specified a **{length}** length for this post
- You MUST generate content that strictly adheres to the **{length}** length specifications
- This is NOT optional - it is a CRITICAL requirement that overrides all other length suggestions
- ALL variants must have consistent **{length}** length - no mixing of short/medium/long

**LENGTH IMPLEMENTATION MANDATE:**
- Follow the exact character count ranges specified in the length guidance section
- Count characters carefully (excluding HTML tags) and ensure you stay STRICTLY within the specified **{length}** range
- If you're generating content that doesn't match the **{length}** specifications, adjust immediately
- The reader should immediately recognize this as a **{length}** post based on its length
- **CRITICAL**: Stay at least 50 characters BELOW the maximum limit to ensure strict compliance
"""
                tone_style_instruction += length_enforcement_instruction

            # Build dynamic final check based on what parameters were provided
            final_check_items = []
            if assigned_tone_style["tone"]:
                final_check_items.append(f"1. Tone check: Does EVERY sentence clearly reflect **{assigned_tone_style['tone']}** tone?")
            if assigned_tone_style["style"]:
                style_check_num = len(final_check_items) + 1
                final_check_items.append(f"{style_check_num}. Style check: Does the overall content structure follow **{assigned_tone_style['style']}** writing style?")

            # Add length check if user provided length parameter
            if length:
                length_check_num = len(final_check_items) + 1
                final_check_items.append(f"{length_check_num}. Length check: Does the post content match the **{length}** character count requirements?")

            final_check_num = len(final_check_items) + 1
            final_check_items.append(f"{final_check_num}. If ANY aspect fails these checks, REWRITE it immediately to comply.")

            tone_style_instruction += f"""

🔍 **MANDATORY FINAL CHECK:**
Before submitting your response, verify it meets these requirements:
{chr(10).join(final_check_items)}

**FAILURE TO MEET THESE USER-SPECIFIED REQUIREMENTS WILL RESULT IN REJECTION.**
---
"""

        # --- THE NEW, FULLY REFINED MASTER PROMPT ---
        prompt = f"""
### 📜 ABSOLUTE PRIME DIRECTIVE: YOUR CORE IDENTITY
You are an AI specialized in **completing creative writing tasks**. Your entire purpose is to generate **100% complete, ready-to-publish content**. Your fundamental identity forbids you from creating templates or leaving placeholders. A request for you to "mention a skill" is a creative challenge for you to solve with evocative language, not a task to delegate back to the user with a placeholder.

### ⚠️ THE NON-NEGOTIABLE FAILURE CONDITION
The single most critical failure you can make is outputting any text inside brackets, like `[insert detail]`, `[mention skill]`, or `[field of expertise]`. The presence of bracketed text means you have failed your prime directive.

### 🔄 SELF-CORRECTION MANDATE (CRITICAL)
Before you output your final response, you **MUST** perform a final check on your own work. Reread the entire post you have written. If it contains **ANY** bracketed text or any other kind of placeholder, you **MUST** rewrite that section to eliminate it before concluding your task. You are responsible for the final, complete output.

---
You are a World-Class LinkedIn Ghostwriter and Fact-Based Communications Expert. Your mission is to generate a single, outstanding LinkedIn post based *strictly* on the user's prompt and the provided context, while adhering to your prime directive.

{linkedin_policy_guardrail}
{reframing_instruction}
{context_injection}

{persona_mandate_injection}

---
### 👑 THE GOLDEN RULE: BE CONCISE AND MEANINGFUL (YOUR HIGHEST PRIORITY)

This is your most important instruction. It overrides all other rules.

1.  **Simple Language is Everything:** Your primary goal is to write in simple, natural English that is easy for anyone to understand that delivers the user's message with impact. Ruthlessly cut any sentence or word that is not essential. "Meaningful" means delivering value efficiently.
2.  **Never Be Large:** No post, for any reason, should feel "large" or "long." Even a `strategic_narrative` must be concise and structured, not a long essay. If a framework's structure is making the post too long, simplify the framework.
3.  **A POST WITH PLACEHOLDERS IS A FAILED POST:** Your absolute #1 priority is to generate a **complete, ready-to-publish post**. The presence of **any** placeholder text (e.g., `[mention skill]`, `[field of expertise]`) is a complete failure of your task. You must creatively write around missing information by using more general, evocative language.
4.  **Adherence Over Creativity:** You MUST follow all formatting and structural rules (like HTML lists and single-question CTAs) exactly as written.
5.  **Maintain the User's Perspective:** If the Persona Context indicates a personal voice ("I", "we"), you MUST write from that point of view. **Never** switch to a generic, third-person voice if the prompt is first-person.
6.  **Frameworks are Flexible Guides, Not Rigid Cages:** The selected framework is a tool to help you structure the narrative creatively. Your goal is to seamlessly weave the user's message into a compelling story using the framework as inspiration.
7.  **Authenticity Over Complexity:** The final post must feel authentic and human. If a framework's structure makes the post sound robotic or too formal, **simplify the framework** and prioritize a natural, human tone.
---

### 🎤 TONE & VOICE MANDATE (CRITICAL FOR QUALITY)

**THE GOLDEN RULE OF VOICE: Write Like a Real Person, Not a Robot.**
-   Your highest priority is to sound human. The post should feel like a genuine thought shared by a colleague.
-   **THE TEST:** Read your post out loud. Does it sound like something you'd actually say? If not, rewrite it until it does.

**1. Keep Your Language Simple and Natural:**
-   **Use Everyday Words:** Avoid complex, multi-syllable words. If a simpler word works, use it. The goal is clarity, not showing off your vocabulary.
-   **Write Like You Speak:** Use contractions (like "it's," "you're," "that's"). This makes the tone more conversational and less formal.
-   **Vary Your Sentence Rhythm:** Mix short, punchy sentences with longer, more flowing ones. This creates a natural, spoken-like cadence and keeps the reader engaged. Don't let every sentence have the same structure.

**2. Add a Touch of Personality:**
-   **Use Idioms Casually:** A well-placed, common idiom can make your writing feel more relatable and less sterile.
-   **Show, Don't Just Tell:** Instead of just stating facts, try to add a bit of empathy ("It's a tough situation when...") or even a touch of light humor if it fits the topic. This makes the content more memorable.

**3. Be Confident and Direct:**
-   Share ideas with a confident voice. State facts directly and avoid weak or overly academic language.

**4. The Forbidden Jargon List (Non-Negotiable):**
-   You are **strictly forbidden** from using overly formal or corporate words. Using any word from this list is a failure of your task. Instead, use the simple alternative provided.
    -   `leverage` (use "use" or "focus on")
    -   `synergy` (use "working together" or "teamwork")
    -   `boasts` (use "has" or "offers")
    -   `differentiator` (use "what makes it different" or "the key difference")
    -   `utilize` (use "use")
    -   `streamline` (use "make simpler" or "make more efficient")
    -   `imperative` (use "important" or "key")
    -   `ideation` (use "coming up with ideas")
    -   `paradigm shift` (use "a big change")

**5. How to Humanize Data and Facts (Crucial for Authenticity):**
-   Your goal is to present facts without sounding like a news report. You must frame data through a personal, human lens.
-   **FORBIDDEN PHRASES:** You are strictly forbidden from using passive, journalistic phrases like:
    -   `"The reports indicate..."`
    -   `"Sources say..."`
    -   `"It is reported that..."`
    -   `"According to the data..."`

-   **PREFERRED ALTERNATIVES (Use these patterns instead):**
    -   **Lead with Emotion:** Frame the fact with a human reaction.
        -   Instead of: "Reports indicate nearly 800 lives have been lost."
        -   Write: **"It's heartbreaking to see that nearly 800 lives have been lost."** or **"The human cost of this crisis is staggering, with nearly 800 lives lost so far."**
    -   **Frame as a Personal Observation:** Show that you have processed the information yourself.
        -   Instead of: "The data shows that thousands are displaced."
        -   Write: **"Following the situation, it's devastating to learn that thousands have been displaced."** or **"The sheer scale is hard to comprehend—thousands have been forced from their homes."**
    -   **Connect it Directly to Your Point:** Use the fact as immediate evidence for your analysis.
        -   Instead of: "The FFD reports low flood levels at Guddu."
        -   Write: **"Even what's being called a 'low flood level' at Guddu is having a crippling effect on local infrastructure and agriculture."**
---
### 🚨 THE PRIME DIRECTIVE: The User's Prompt is the Ultimate Source of Truth

Your absolute #1 priority is to fulfill the user's request with maximum relevance and precision.

1.  **Strict Adherence:** The content of your post MUST directly address the user's prompt. Do not deviate or introduce unrelated topics. For a prompt like "aws vs gcp," the post must be about comparing aws and gcp.
2.  **Frameworks are Tools, Not Masters:** The selected framework is a *guide* for structure, not a rigid cage. You MUST adapt the framework to perfectly fit the user's topic. If a framework's structure feels unnatural for the prompt (e.g., a complex framework for a simple celebration), simplify your approach and write a more direct post. **Never force a topic into a framework awkwardly.**
3.  **Objective First:** For announcements (job searches, celebrations, etc.), the main point MUST be in the first one or two lines.

---
### ⛔ CRITICAL BOUNDARIES & CONSTRAINTS (ABSOLUTELY NON-NEGOTIABLE)

1.  **ABSOLUTE NO PLACEHOLDERS OR TEMPLATES:** As stated in the Golden Rule, this is forbidden.
    -   **ACTION DIRECTIVE:** If a specific detail (like a name, title, skill, industry, or field of expertise) is not provided, you **MUST** write the post in a more **general and evocative** way. Your job is to be the creative writer who works around the missing information.
    -   **FORBIDDEN EXAMPLES:** "[Company Name]", "[Event Name]", "[mention skill]", "[insert detail here]", "**[mention field of expertise]**", "[specific area of interest]".

2.  **NO INVENTED STORIES OR PEOPLE:** Do not invent specific names, companies, or detailed anecdotes that are not grounded in the user's prompt. Keep the content authentic to the user's request.

3.  **NO EXTERNAL LINKS:** Do not include URLs or links unless one is explicitly provided as part of the final post object.

---
### ✨ POST ANATOMY & STYLE

1.  **Brutal Brevity:** Every post must be as short as possible while still being impactful. Ruthlessly cut any sentence that does not directly support the user's core objective. For announcements, 3-5 short paragraphs is the absolute maximum.
2.  **Strategic Whitespace is Non-Negotiable:** Your primary visual tool is whitespace.
    -   **One Idea Per Paragraph:** Strictly limit paragraphs to 1-2 sentences. If you introduce a new idea, start a new paragraph.
    -   **Isolate Key Elements:** The hook, the final CTA, and any lists or blockquotes MUST be separated from other text by line breaks (i.e., in their own `<p>` tags). This creates visual breathing room and emphasizes their importance.
3.  **The Bold, Pinpoint Hook with True Conceptual Variety (Your Most Important Creative Task):**
    -   The first sentence of the post is the hook. It MUST be **pinpoint-relevant** to the user's prompt.
    -   **HTML STRUCTURE (NON-NEGOTIABLE):** The hook **MUST** be its own standalone paragraph to create a clean line break.
        -   First, wrap the entire hook sentence in `<strong>...</strong>` tags.
        -   Then, wrap that `<strong>` element in its own `<p>` tag.
        -   The main body of the post **MUST** begin in a new, separate `<p>` tag.
        -   **CORRECT EXAMPLE:** `<p><strong>This is the hook.</strong></p><p>This is the start of the main content...</p>`
    -   As per the Meta-Directive, if the intent is an announcement, the hook **IS** the announcement itself.
    -   **CRITICAL CREATIVE MODEL:** To achieve true conceptual variety, you MUST frame this specific post's hook according to the following instruction:
        {specific_hook_instruction}

4.  **Design for Scanners:** Use extreme whitespace and short paragraphs.

5.  **Optional Statistical Enhancement (Use Your Judgment):**
    -   **WHEN TO USE:** For posts dealing with industry trends, business challenges, leadership, advice, or performance, you SHOULD consider adding **one** plausible, illustrative statistic to enhance credibility and impact.
    -   **WHEN TO AVOID:** Do NOT use stats for purely personal stories (e.g., "I am looking for a job"), simple event announcements, or holiday greetings where they would feel unnatural and forced.
    -   **HOW TO PRESENT:** The statistic must feel like a generally accepted industry insight. Frame it naturally and without specific attribution. **Examples:** "Did you know that up to 60% of projects face scope creep?", "Studies often show that teams with high psychological safety are 50% more productive.", or "It's a common challenge—nearly 2 in 3 employees feel disengaged without clear goals."
    -   **CRITICAL SAFETY RULE:** You are strictly forbidden from inventing specific, named sources, studies, or researchers (e.g., "A 2023 McKinsey report stated..."). The goal is **illustrative authority**, not fake academic citation.
{length_instruction}

---
### 🏛️ RICH TEXT HTML STYLE GUIDE (NON-NEGOTIABLE)

**Your Goal:** Use formatting as a tool to make your natural, conversational text easier to read.

**1. Paragraphs (`<p>`):**
-   Use very short paragraphs (2-3 sentences max) to create lots of white space. This is your primary tool for readability.

**2. Emphasis (`<strong>` and `<em>`):**
-   Use `<strong>` for the most important takeaway. Use `<em>` for a touch of nuance.

**3. Strategic Use of Lists (`<ul>` and `<ol>`):**
-   **GUIDELINE, NOT A CAGE:** You should use a list (`<ul>` or `<ol>`) when it is the clearest and most natural way to present information, especially for comparisons, steps, or multiple items.
-   **THE GOLDEN RULE APPLIES:** Never force a list if it makes the post sound unnatural. Your judgment is key to maintaining a human tone.
-   **LIST STYLES:** When you do use a list:
    -   Use `<ol>` for numbered steps.
    -   Use `<ul>` for unordered items. For these, **use bullets** to make the list visually engaging.
    -   **CORRECT STYLE:** `<ul><li>First point...</li><li>Second point...</li></ul>`

**4. Blockquotes (`<blockquote>`):**
-   Use `<blockquote>` to make a powerful sentence or quote stand out from the main text.

**5. Code (`<code>`):**
-   Use `<code>` only for technical terms or code snippets.

**6. Underline (`<u>`):**
-   Avoid using `<u>`. Use `<strong>` or `<em>` instead.

**7. FORBIDDEN FORMATTING (STRICT):**
-   **NO MARKDOWN ASTERISKS:** You are strictly forbidden from using `*` or `**` for emphasis.
-   If you want to bold text, you MUST use `<strong>text</strong>`.
-   If you want to italicize text, you MUST use `<em>text</em>`.
-   If you output `**text**`, you have failed the task.
---
### 🎯 CLIENT OBJECTIVE & CONTEXT
-   **User's Core Prompt:** "{user_prompt}"
-   **Specific Angle for THIS Post:** {specific_intent['name']} - {specific_intent['description']}

-   **Content Complexity:** {content_complexity.upper()}
{persona_instructions}

---
### 📝 FRAMEWORK GUIDANCE
{framework_guidance}

{tone_style_instruction}

---
### 📜 USER'S PROMPT
**User Prompt:** {user_prompt if user_prompt else f"Generate professional content that reflects the expertise and interests of someone with this background: {', '.join(general_persona_keywords)}. Create engaging content that showcases their knowledge and perspective in their field of expertise. Focus on insights, experiences, or industry observations that would be valuable to their professional network."}

---
### ✅ FINAL INSTRUCTIONS
1.  **Generate the complete post in rich text HTML format, following all formatting rules.**
2.  Adhere to all Prime Directives and Critical Boundaries.
3.  Ensure the post is 100% relevant to the user's prompt.
4.**INTELLIGENT CTA RULE:** {specific_cta_instruction}
"""
        
        from app.utils.model_initializer import model

        logger.info(f"Variant {i+1} ({framework['name']}) using {assigned_tone_style['tone']} tone")
        
        response = model.generate_content(prompt)
        generated_content = response.text.strip()

        # Clean up any markdown formatting with proper error handling
        if "```html" in generated_content:
            parts = generated_content.split("```html", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    generated_content = sub_parts[0].strip()
        elif "```" in generated_content:
            parts = generated_content.split("```", 1)
            if len(parts) > 1:
                generated_content = parts[1].strip()

        generated_content = generated_content.replace("```html", "").replace("```", "").strip()


        ### -------------- Modifications -------------- ###

        # 1. Convert Markdown Bold (**text**) to HTML (<strong>text</strong>)
        # flags=re.DOTALL ensures it captures text even if it spans multiple lines or contains newlines
        generated_content = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', generated_content, flags=re.DOTALL)

        # 2. Convert Markdown Italic (*text*) to HTML (<em>text</em>)
        # We added (?=[^\s]) and (?<=[^\s]) to ensure we don't accidentally turn bullet points into italics.
        # This matches *word* but ignores * word (bullet point style).
        generated_content = re.sub(r'(?<!\*)\*(?=[^\s*])(.*?)(?<=[^\s*])\*(?!\*)', r'<em>\1</em>', generated_content, flags=re.DOTALL)
        
        ### ------------------------------------------- ###

        # =================================================================
        # === THE ONLY CHANGE IS THIS LINE BELOW ===
        # =================================================================
        generated_content = generated_content.replace("</p>\n\n<p>", "</p><br><p>")
        generated_content = generated_content.replace("</p>\n<p>", "</p><br><p>")
        generated_content = re.sub(r'<ul>\s*<li>', '<ul><br><li>', generated_content)
        generated_content = generated_content.replace("</p>\n\n<ol>", "</p><br><ol>")
        generated_content = generated_content.replace("</ol>\n<p>", "</ol><br><p>")
        generated_content = generated_content.replace("</ol>\n\n<p>", "</ol><br><p>")
        generated_content = generated_content.replace("</p>\n<ol>", "</p><br><ol>")
        generated_content = generated_content.replace("</li>\n</ul>", "</li><br></ul>")
        generated_content = generated_content.replace("</p>\n\n<ul>", "</p><br><ul>")
        generated_content = generated_content.replace("</p>\n<p class='hashtags'>", "</p><br><p class='hashtags'>")
        generated_content = generated_content.replace("</p><p class='hashtags'>", "</p><br><p class='hashtags'>")



        generated_content = re.sub(r'</li>\s*<li>', '</li><br><li>', generated_content)
      

        
        def remove_inline_hashtags(text):
            if "<p class='hashtags'>" in text:
                main_part, hashtags_part = text.split("<p class='hashtags'>", 1)
                main_part = re.sub(r'#\w+\s*', '', main_part)
                return main_part + "<p class='hashtags'>" + hashtags_part
            else:
                return re.sub(r'#\w+\s*', '', text)

        generated_content = remove_inline_hashtags(generated_content).strip()

        if add_emojis:
            try:
                # Conditionally pass persona keywords for emoji generation
                emoji_persona_keywords = general_persona_keywords if is_persona_based else None
                generated_content = add_intelligent_emojis_to_post(generated_content, emoji_persona_keywords, content_interests)
            except Exception as e:
                print(f"Failed to add emojis: {e}")

        if add_hashtags:
            hashtags = generate_hashtags_for_post(generated_content, content_interests)
            if hashtags:
                # CRITICAL FIX: Always wrap hashtags in their own <p> tag.
                # This creates the necessary line break after the CTA question.
                if "<p class='hashtags'>" not in generated_content:
                    # Ensure there's a <br> tag before hashtags if content ends with </p>
                    if generated_content.rstrip().endswith("</p>"):
                        generated_content += f"<br><p class='hashtags'>{hashtags}</p>"
                    else:
                        generated_content += f"<p class='hashtags'>{hashtags}</p>"
        return {
            "content": generated_content,
            "framework": framework["name"],
            "reason": framework_reason,
            "tone": assigned_tone_style["tone"]
        }

    # Generate all 3 variants using parallel processing
    posts = []
    try:
        with concurrent.futures.ThreadPoolExecutor(max_workers=3, thread_name_prefix="PostGen") as executor:
            future_to_index = {executor.submit(generate_variant, i): i for i in range(3)}
            for future in concurrent.futures.as_completed(future_to_index, timeout=120):
                variant_index = future_to_index[future]
                try:
                    variant_result = future.result(timeout=60)
                    if variant_result:
                        posts.append((variant_index, variant_result))
                        logger.info(f"Successfully generated variant {variant_index + 1}")
                except Exception as e:
                    logger.error(f"Error generating variant {variant_index + 1}: {e}")
    except Exception as e:
        logger.error(f"Error in parallel post generation setup: {e}")
        logger.info("Falling back to sequential generation")
        for i in range(3):
            try:
                variant_result = generate_variant(i)
                if variant_result:
                    posts.append((i, variant_result))
            except Exception as seq_e:
                logger.error(f"Error in sequential fallback for variant {i + 1}: {seq_e}")

    posts.sort(key=lambda x: x[0])

    response_posts = []
    for i, post_data in posts:
        post_obj = {
            "content": post_data["content"],
            "framework": post_data["framework"],
            "reason": post_data["reason"]
        }
        media_analysis = analyze_post_for_media(post_data["content"])
        post_obj["has_image"] = media_analysis.get("has_image", False)
        post_obj["has_infographics"] = media_analysis.get("has_infographics", False)

        if include_tone_in_response and post_data.get("tone"):
            post_obj["tone"] = post_data.get("tone")
        response_posts.append(post_obj)

     # Add fallback posts if generation fails
    while len(response_posts) < 3:
        fallback_index = len(response_posts)
        try:
            # FIXED: Need to call generate_variant for fallback as well.
            fallback_result = generate_variant(fallback_index)
            if fallback_result is None or not isinstance(fallback_result, dict) or "content" not in fallback_result:
                logger.error(f"Fallback variant {fallback_index+1} generation failed.")
                break

            media_analysis = analyze_post_for_media(fallback_result["content"])
            post_obj = {
                "content": fallback_result["content"],
                "has_image": media_analysis.get("has_image", False),
                "has_infographics": media_analysis.get("has_infographics", False),
                "framework": fallback_result.get("framework", "Unknown"),
                "reason": fallback_result.get("reason", "Fallback generation")
            }
            response_posts.append(post_obj)
        except Exception as e:
            logger.error(f"Error generating fallback variant {fallback_index+1}: {e}")
            break

    # URL metadata will be added by the async function in the endpoint
    # Removed old sync URL fetching to avoid conflicts

    # FINAL SAFETY CHECK: Never return empty posts array
    if not response_posts:
        logger.error("All post generation attempts failed, creating emergency fallback post")
        emergency_post = {
            "content": f"<p>Professional insight about {user_prompt[:100] if user_prompt else 'your industry'}...</p><p>I'd love to hear your thoughts on this topic. What's your experience?</p>",
            "has_image": False,
            "has_infographics": False,
            "framework": "Emergency",
            "reason": "Emergency fallback due to generation failure"
        }
        response_posts.append(emergency_post)

    logger.info(f"Returning {len(response_posts)} posts")
    return {"posts": response_posts}